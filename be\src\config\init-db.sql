-- Create database if not exists
CREATE DATABASE IF NOT EXISTS aitrainerhub;
USE aitrainerhub;

-- Create user table
CREATE TABLE IF NOT EXISTS user (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(255) UNIQUE NOT NULL,
    nama VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create experts table
CREATE TABLE IF NOT EXISTS experts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    model VARCHAR(100) DEFAULT 'gpt-4o-mini',
    assistant_id VARCHAR(255),
    image_url VARCHAR(500),
    pricing_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_public B<PERSON><PERSON>EAN DEFAULT FALSE,
    labels <PERSON><PERSON><PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE
);

-- Insert sample user with token 'abcde'
INSERT IGNORE INTO user (token, nama) VALUES ('abcde', 'Test User');

-- Show created tables
DESCRIBE user;
DESCRIBE experts;