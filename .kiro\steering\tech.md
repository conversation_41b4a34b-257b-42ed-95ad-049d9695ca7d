# Technology Stack

## Backend (be/)

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL with mysql2 driver
- **Authentication**: Token-based authentication middleware
- **File Upload**: Multer for multipart/form-data handling
- **AI Integration**: OpenAI SDK v4.67.0
- **Environment**: dotenv for configuration management

### Backend Architecture Pattern
- **MVC Structure**: Controllers, Services, Routes separation
- **Middleware**: CORS, error handling, authentication
- **Configuration**: Centralized in config/ directory
- **Utilities**: Database initialization and helper functions

## Frontend (fe/)

- **Framework**: Next.js 15.4.4 with React 19
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4
- **UI Components**: Radix UI primitives
- **State Management**: TanStack React Query v5
- **Icons**: Lucide React
- **Build Tool**: Turbopack (Next.js turbo mode)

### Frontend Architecture Pattern
- **App Router**: Next.js 13+ app directory structure
- **Component Structure**: Reusable components in components/
- **API Layer**: Centralized API functions in lib/api.ts
- **Path Aliases**: @/* mapped to src/*

## Development Commands

### Backend Development
```bash
cd be
npm install          # Install dependencies
npm run dev          # Start with nodemon (auto-reload)
npm start           # Production start
```

### Frontend Development
```bash
cd fe
npm install          # Install dependencies
npm run dev          # Start development server with Turbopack
npm run build        # Production build
npm start           # Production start
npm run lint        # ESLint checking
```

### Environment Setup
- Backend: `.env` file with OPENAI_API_KEY, ASSISTANT_ID, PORT, FRONTEND_URL, DB_* configs
- Frontend: `.env.local` file with NEXT_PUBLIC_API_URL

## Code Style Conventions

- **Backend**: CommonJS modules (require/module.exports)
- **Frontend**: ES modules with TypeScript
- **Error Handling**: Centralized error middleware in backend
- **API Responses**: Consistent JSON structure with success/error patterns
- **File Organization**: Feature-based grouping (controllers, services, routes)