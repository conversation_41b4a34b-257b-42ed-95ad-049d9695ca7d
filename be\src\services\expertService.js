const { pool } = require('../config/database');
const assistantService = require('./assistantService');

class ExpertService {
  async createExpert(expertData, knowledgeBaseFile = null, imageFile = null) {
    try {
      const { userId, name, description, systemPrompt, model, pricingPercentage, isPublic, labels } = expertData;

      // Create OpenAI assistant
      const assistantResult = await assistantService.processAssistantCreation({
        name,
        instructions: systemPrompt,
        model: model || 'gpt-4o-mini',
        userId
      }, knowledgeBaseFile);

      if (!assistantResult.success) {
        throw new Error('Failed to create OpenAI assistant');
      }

      // Process image file if provided
      let imageUrl = null;
      if (imageFile) {
        // Store relative path to the uploaded image
        imageUrl = `/uploads/${imageFile.filename}`;
      }

      // Save expert to database
      const query = `
        INSERT INTO experts (user_id, name, description, system_prompt, model, assistant_id, image_url, pricing_percentage, is_public, labels)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        userId,
        name,
        description || '',
        systemPrompt,
        model || 'gpt-4o-mini',
        assistantResult.assistant.id,
        imageUrl,
        pricingPercentage || 0.00,
        isPublic || false,
        labels ? JSON.stringify(labels) : null
      ];

      const [result] = await pool.execute(query, values);

      return {
        success: true,
        expert: {
          id: result.insertId,
          userId,
          name,
          description,
          systemPrompt,
          model: model || 'gpt-4o-mini',
          assistantId: assistantResult.assistant.id,
          imageUrl,
          pricingPercentage: pricingPercentage || 0.00,
          isPublic: isPublic || false,
          labels: labels || [],
          createdAt: new Date()
        }
      };
    } catch (error) {
      console.error('Expert creation error:', error);
      throw error;
    }
  }

  async listUserExperts(userId) {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id, 
               image_url, pricing_percentage, is_public, labels, created_at, updated_at
        FROM experts 
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;

      const [rows] = await pool.execute(query, [userId]);

      return {
        success: true,
        experts: rows.map(row => {
          let labels = [];
          if (row.labels) {
            try {
              labels = JSON.parse(row.labels);
            } catch (error) {
              // Handle invalid JSON - try to convert comma-separated string to array
              if (typeof row.labels === 'string') {
                labels = row.labels.split(',').map(label => label.trim()).filter(label => label);
              }
              console.warn(`Invalid JSON in labels for expert ${row.id}:`, row.labels);
            }
          }
          
          return {
            id: row.id,
            name: row.name,
            description: row.description,
            systemPrompt: row.system_prompt,
            model: row.model,
            assistantId: row.assistant_id,
            imageUrl: row.image_url,
            pricingPercentage: row.pricing_percentage,
            isPublic: row.is_public || false,
            labels: Array.isArray(labels) ? labels : [],
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };
        })
      };
    } catch (error) {
      console.error('List experts error:', error);
      throw error;
    }
  }

  async getExpert(expertId, userId) {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id, 
               image_url, pricing_percentage, is_public, labels, created_at, updated_at
        FROM experts 
        WHERE id = ? AND user_id = ?
      `;

      const [rows] = await pool.execute(query, [expertId, userId]);

      if (rows.length === 0) {
        return {
          success: false,
          error: 'Expert not found'
        };
      }

      const expert = rows[0];
      return {
        success: true,
        expert: {
          id: expert.id,
          name: expert.name,
          description: expert.description,
          systemPrompt: expert.system_prompt,
          model: expert.model,
          assistantId: expert.assistant_id,
          imageUrl: expert.image_url,
          pricingPercentage: expert.pricing_percentage,
          isPublic: expert.is_public || false,
          labels: (() => {
            if (!expert.labels) return [];
            try {
              const parsed = JSON.parse(expert.labels);
              return Array.isArray(parsed) ? parsed : [];
            } catch (error) {
              // Handle invalid JSON - try to convert comma-separated string to array
              if (typeof expert.labels === 'string') {
                return expert.labels.split(',').map(label => label.trim()).filter(label => label);
              }
              console.warn(`Invalid JSON in labels for expert ${expert.id}:`, expert.labels);
              return [];
            }
          })(),
          createdAt: expert.created_at,
          updatedAt: expert.updated_at
        }
      };
    } catch (error) {
      console.error('Get expert error:', error);
      throw error;
    }
  }

  async updateExpert(expertId, userId, updateData, knowledgeBaseFile = null, imageFile = null) {
    try {
      // First check if expert exists and belongs to user
      const existingExpert = await this.getExpert(expertId, userId);
      if (!existingExpert.success) {
        return existingExpert;
      }

      const expert = existingExpert.expert;
      
      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];

      if (updateData.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(updateData.name);
      }

      if (updateData.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(updateData.description);
      }

      if (updateData.systemPrompt !== undefined) {
        updateFields.push('system_prompt = ?');
        updateValues.push(updateData.systemPrompt);
        
        // Update OpenAI assistant instructions if system prompt changed
        try {
          await assistantService.updateAssistantInstructions(expert.assistantId, updateData.systemPrompt);
        } catch (error) {
          console.warn('Failed to update assistant instructions:', error.message);
        }
      }

      if (updateData.model !== undefined) {
        updateFields.push('model = ?');
        updateValues.push(updateData.model);
      }

      if (updateData.pricingPercentage !== undefined) {
        updateFields.push('pricing_percentage = ?');
        updateValues.push(updateData.pricingPercentage);
      }

      if (updateData.isPublic !== undefined) {
        updateFields.push('is_public = ?');
        updateValues.push(updateData.isPublic);
      }

      if (updateData.labels !== undefined) {
        updateFields.push('labels = ?');
        updateValues.push(JSON.stringify(updateData.labels));
      }

      // Process image file if provided
      if (imageFile) {
        const imageUrl = `/uploads/${imageFile.filename}`;
        updateFields.push('image_url = ?');
        updateValues.push(imageUrl);
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      if (updateFields.length === 1) { // Only updated_at was added
        return {
          success: false,
          error: 'No fields to update'
        };
      }

      // Add expertId and userId to values for WHERE clause
      updateValues.push(expertId, userId);

      const query = `
        UPDATE experts 
        SET ${updateFields.join(', ')}
        WHERE id = ? AND user_id = ?
      `;

      const [result] = await pool.execute(query, updateValues);

      if (result.affectedRows === 0) {
        return {
          success: false,
          error: 'Expert not found or no changes made'
        };
      }

      // Handle knowledge base file update if provided
      if (knowledgeBaseFile) {
        try {
          await assistantService.updateAssistantKnowledgeBase(expert.assistantId, knowledgeBaseFile);
        } catch (error) {
          console.warn('Failed to update knowledge base:', error.message);
        }
      }

      // Return updated expert
      return await this.getExpert(expertId, userId);

    } catch (error) {
      console.error('Expert update error:', error);
      throw error;
    }
  }

  validateExpertData(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.systemPrompt || data.systemPrompt.trim() === '') {
      errors.push('System prompt is required');
    }

    if (data.model && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    if (data.pricingPercentage && (isNaN(data.pricingPercentage) || data.pricingPercentage < 0 || data.pricingPercentage > 100)) {
      errors.push('Pricing percentage must be between 0 and 100');
    }

    if (data.labels && Array.isArray(data.labels)) {
      if (data.labels.length > 5) {
        errors.push('Maximum 5 labels allowed');
      }
      
      for (const label of data.labels) {
        if (typeof label !== 'string' || label.trim() === '') {
          errors.push('All labels must be non-empty strings');
          break;
        }
        if (label.length > 50) {
          errors.push('Each label must be 50 characters or less');
          break;
        }
      }
    }

    return errors;
  }

  validateExpertUpdateData(data) {
    const errors = [];

    if (data.name !== undefined && (!data.name || data.name.trim() === '')) {
      errors.push('Name cannot be empty');
    }

    if (data.systemPrompt !== undefined && (!data.systemPrompt || data.systemPrompt.trim() === '')) {
      errors.push('System prompt cannot be empty');
    }

    if (data.model !== undefined && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    if (data.pricingPercentage !== undefined && (isNaN(data.pricingPercentage) || data.pricingPercentage < 0 || data.pricingPercentage > 100)) {
      errors.push('Pricing percentage must be between 0 and 100');
    }

    if (data.labels !== undefined && Array.isArray(data.labels)) {
      if (data.labels.length > 5) {
        errors.push('Maximum 5 labels allowed');
      }
      
      for (const label of data.labels) {
        if (typeof label !== 'string' || label.trim() === '') {
          errors.push('All labels must be non-empty strings');
          break;
        }
        if (label.length > 50) {
          errors.push('Each label must be 50 characters or less');
          break;
        }
      }
    }

    return errors;
  }

  isValidModel(model) {
    const validModels = [
      'gpt-3.5-turbo',
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4-turbo-preview',
      'gpt-4o',
      'gpt-4o-mini'
    ];
    return validModels.includes(model);
  }
}

module.exports = new ExpertService();