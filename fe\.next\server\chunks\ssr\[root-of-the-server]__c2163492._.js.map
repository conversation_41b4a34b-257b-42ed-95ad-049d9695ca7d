{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ApiExample.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApiExample.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApiExample.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ApiExample.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApiExample.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApiExample.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport ApiExample from \"@/components/ApiExample\";\nimport Script from \"next/script\";\n\nexport default function Home() {\n  return (\n    <div className=\"font-sans min-h-screen p-8\">\n      <Script src=\"/test-api.js\" />\n      <main className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center mb-8\">\n          <Image\n            className=\"dark:invert mx-auto mb-4\"\n            src=\"/next.svg\"\n            alt=\"Next.js logo\"\n            width={180}\n            height={38}\n            priority\n          />\n          <h1 className=\"text-3xl font-bold mb-2\">AI Trainer Hub</h1>\n          <p className=\"text-gray-600\">Frontend dengan Token Authentication</p>\n        </div>\n        \n        <ApiExample />\n\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8HAAA,CAAA,UAAM;gBAAC,KAAI;;;;;;0BACZ,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,QAAQ;;;;;;0CAEV,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC,gIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}]}