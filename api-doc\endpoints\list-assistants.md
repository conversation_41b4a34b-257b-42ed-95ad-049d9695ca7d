# List Assistants

Endpoint untuk mendapatkan daftar semua assistant.

## Endpoint
```
GET /api/assistants
```

## Request

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| userId | string | Yes | ID user untuk filter assistant milik user |

## Response

### Success (200)
```json
{
  "success": true,
  "assistants": [
    {
      "id": "asst_abc123",
      "name": "Customer Support Bot",
      "instructions": "You are a helpful customer support assistant...",
      "model": "gpt-4o-mini",
      "created_at": 1703123456,
      "tools": [
        {
          "type": "file_search"
        }
      ],
      "file_ids": ["file_abc123"]
    },
    {
      "id": "asst_def456",
      "name": "Code Helper",
      "instructions": "You are a programming assistant...",
      "model": "gpt-4-turbo",
      "created_at": 1703123400,
      "tools": [],
      "file_ids": []
    }
  ]
}
```

### Error (400) - Missing User ID
```json
{
  "success": false,
  "error": "User ID is required"
}
```

### Error (500) - Server Error
```json
{
  "success": false,
  "error": "Failed to list assistants",
  "message": "Detailed error message"
}
```

## Response Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Status keberhasilan operasi |
| assistants | array | Array berisi daftar assistant |
| assistants[].id | string | ID unik assistant |
| assistants[].name | string | Nama assistant |
| assistants[].instructions | string | Instruksi assistant |
| assistants[].model | string | Model yang digunakan |
| assistants[].created_at | number | Timestamp pembuatan (Unix timestamp) |
| assistants[].tools | array | Tools yang tersedia untuk assistant |
| assistants[].file_ids | array | Array ID file yang terkait dengan assistant |

## Contoh Penggunaan

### cURL
```bash
curl -X GET "http://localhost:3001/api/assistants?userId=user_123"
```

### JavaScript (Fetch)
```javascript
const response = await fetch('http://localhost:3001/api/assistants');
const data = await response.json();
console.log(data.assistants);
```

### Response Example
```json
{
  "success": true,
  "assistants": [
    {
      "id": "asst_1",
      "name": "Customer Support",
      "instructions": "You are a helpful customer support assistant",
      "model": "gpt-4o-mini",
      "created_at": 1703123456,
      "tools": [{"type": "file_search"}],
      "file_ids": ["file_1"]
    },
    {
      "id": "asst_2", 
      "name": "Code Helper",
      "instructions": "You help with programming questions",
      "model": "gpt-4-turbo",
      "created_at": 1703123400,
      "tools": [],
      "file_ids": []
    }
  ]
}
```

## Catatan
- Assistant diurutkan berdasarkan tanggal pembuatan (terbaru dulu)
- Maksimal 20 assistant per request
- file_ids berisi ID file yang diupload saat membuat assistant