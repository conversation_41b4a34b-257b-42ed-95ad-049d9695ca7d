{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/api.ts"], "sourcesContent": ["const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\ninterface ApiOptions {\r\n    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\r\n    body?: any;\r\n    headers?: Record<string, string>;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n    const { method = 'GET', body, headers = {} } = options;\r\n\r\n    const fullUrl = `${API_URL}${endpoint}`;\r\n\r\n    // Debug logging\r\n    console.log('🔍 API Call Debug:', {\r\n        endpoint,\r\n        fullUrl,\r\n        API_URL,\r\n        TOKEN: TOKEN.substring(0, 3) + '***',\r\n        method,\r\n        body,\r\n        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,\r\n        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN\r\n    });\r\n\r\n    const config: RequestInit = {\r\n        method,\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${TOKEN}`,\r\n            ...headers,\r\n        },\r\n    };\r\n\r\n    if (body && method !== 'GET') {\r\n        config.body = JSON.stringify(body);\r\n    }\r\n\r\n    try {\r\n        const response = await fetch(fullUrl, config);\r\n\r\n        console.log('📡 Response status:', response.status, response.statusText);\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            console.error('❌ API Error:', errorData);\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('✅ API Success:', data);\r\n        return data;\r\n    } catch (error) {\r\n        console.error('💥 API call failed:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n    // Health check (no token required)\r\n    health: () => apiCall('/health'),\r\n\r\n    // Chat endpoints (token required)\r\n    chat: (message: string, threadId?: string, expertId?: string, expertContext?: any) => apiCall('/api/chat', {\r\n        method: 'POST',\r\n        body: { message, threadId, expertId, expertContext }\r\n    }),\r\n\r\n    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n    // Chat session endpoints\r\n    getUserChatSessions: (limit?: number) => apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ''}`),\r\n\r\n    getActiveSessionForExpert: (expertId: string) => apiCall(`/api/chat/sessions/expert/${expertId}`),\r\n\r\n    updateSessionTitle: (sessionId: string, title: string) => apiCall(`/api/chat/sessions/${sessionId}/title`, {\r\n        method: 'PUT',\r\n        body: { title }\r\n    }),\r\n\r\n    deleteSession: (sessionId: string) => apiCall(`/api/chat/sessions/${sessionId}`, {\r\n        method: 'DELETE'\r\n    }),\r\n\r\n    // Assistant endpoints (token required)\r\n    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),\r\n\r\n    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {\r\n        method: 'POST',\r\n        body: { threadId, message }\r\n    }),\r\n\r\n    runAssistant: (threadId: string) => apiCall('/assistant/run', {\r\n        method: 'POST',\r\n        body: { threadId }\r\n    }),\r\n\r\n    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n    // Expert endpoints (token required)\r\n    createExpert: (expertData: FormData) => {\r\n        return fetch(`${API_URL}/api/experts`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: expertData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n\r\n    listExperts: () => apiCall('/api/experts'),\r\n\r\n    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {\r\n        const formData = new FormData();\r\n        \r\n        // Add text fields\r\n        Object.keys(expertData).forEach(key => {\r\n            if (expertData[key] !== undefined && expertData[key] !== null) {\r\n                if (key === 'labels' && Array.isArray(expertData[key])) {\r\n                    formData.append(key, JSON.stringify(expertData[key]));\r\n                } else {\r\n                    formData.append(key, expertData[key].toString());\r\n                }\r\n            }\r\n        });\r\n\r\n        // Add files\r\n        if (knowledgeBaseFile) {\r\n            formData.append('file', knowledgeBaseFile);\r\n        }\r\n        if (imageFile) {\r\n            formData.append('image', imageFile);\r\n        }\r\n\r\n        return fetch(`${API_URL}/api/experts/${expertId}`, {\r\n            method: 'PUT',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: formData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n};"], "names": [], "mappings": ";;;;AAAgB;AAAhB,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAQxC,eAAe,QAAQ,QAAgB;QAAE,UAAA,iEAAsB,CAAC;IACnE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG;IAE/C,MAAM,UAAU,AAAC,GAAY,OAAV,SAAmB,OAAT;IAE7B,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAC9B;QACA;QACA;QACA,OAAO,MAAM,SAAS,CAAC,GAAG,KAAK;QAC/B;QACA;QACA,iCAAiC;QACjC,+BAA+B;IACnC;IAEA,MAAM,SAAsB;QACxB;QACA,SAAS;YACL,gBAAgB;YAChB,iBAAiB,AAAC,UAAe,OAAN;YAC3B,GAAG,OAAO;QACd;IACJ;IAEA,IAAI,QAAQ,WAAW,OAAO;QAC1B,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC;IACjC;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;QAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACV;AACJ;AAGO,MAAM,MAAM;IACf,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CAAC,SAAiB,UAAmB,UAAmB,gBAAwB,QAAQ,aAAa;YACvG,QAAQ;YACR,MAAM;gBAAE;gBAAS;gBAAU;gBAAU;YAAc;QACvD;IAEA,mBAAmB,CAAC,WAAqB,QAAQ,AAAC,eAAuB,OAAT,UAAS;IAEzE,yBAAyB;IACzB,qBAAqB,CAAC,QAAmB,QAAQ,AAAC,qBAAmD,OAA/B,QAAQ,AAAC,UAAe,OAAN,SAAU;IAElG,2BAA2B,CAAC,WAAqB,QAAQ,AAAC,6BAAqC,OAAT;IAEtF,oBAAoB,CAAC,WAAmB,QAAkB,QAAQ,AAAC,sBAA+B,OAAV,WAAU,WAAS;YACvG,QAAQ;YACR,MAAM;gBAAE;YAAM;QAClB;IAEA,eAAe,CAAC,YAAsB,QAAQ,AAAC,sBAA+B,OAAV,YAAa;YAC7E,QAAQ;QACZ;IAEA,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAAoB,QAAQ,sBAAsB;YAC9E,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC9B;IAEA,cAAc,CAAC,WAAqB,QAAQ,kBAAkB;YAC1D,QAAQ;YACR,MAAM;gBAAE;YAAS;QACrB;IAEA,aAAa,CAAC,WAAqB,QAAQ,AAAC,uBAA+B,OAAT;IAElE,oCAAoC;IACpC,cAAc,CAAC;QACX,OAAO,MAAM,AAAC,GAAU,OAAR,SAAQ,iBAAe;YACnC,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;IAEA,aAAa,IAAM,QAAQ;IAE3B,WAAW,CAAC,WAAqB,QAAQ,AAAC,gBAAwB,OAAT;IAEzD,cAAc,CAAC,UAAkB,YAAiB,mBAAiC;QAC/E,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC3D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACpD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACvD,OAAO;oBACH,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBACjD;YACJ;QACJ;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACnB,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,IAAI,WAAW;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,OAAO,MAAM,AAAC,GAAyB,OAAvB,SAAQ,iBAAwB,OAAT,WAAY;YAC/C,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ExpertProfile.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { api } from '@/lib/api';\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\ninterface ExpertProfileProps {\r\n  expertId: string;\r\n}\r\n\r\nconst ExpertProfile: React.FC<ExpertProfileProps> = ({ expertId }) => {\r\n  const [expert, setExpert] = useState<Expert | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const router = useRouter();\r\n\r\n  const loadExpert = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      const result = await api.getExpert(expertId);\r\n      \r\n      if (result.success) {\r\n        setExpert(result.expert);\r\n      } else {\r\n        setError(result.error || 'Failed to load expert');\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to load expert');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadExpert();\r\n  }, [expertId]);\r\n\r\n  const getExpertIcon = (labels: string[]) => {\r\n    if (labels.includes('business') || labels.includes('marketing')) return '💼';\r\n    if (labels.includes('code') || labels.includes('programming')) return '💻';\r\n    if (labels.includes('creative') || labels.includes('design')) return '🎨';\r\n    if (labels.includes('education') || labels.includes('learning')) return '📚';\r\n    if (labels.includes('health') || labels.includes('medical')) return '🏥';\r\n    if (labels.includes('finance') || labels.includes('money')) return '💰';\r\n    return '🤖';\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const startChat = () => {\r\n    router.push(`/chat?expertId=${expertId}`);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          {/* Navigation */}\r\n          <div className=\"mb-8\">\r\n            <Link \r\n              href=\"/\" \r\n              className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n            >\r\n              <span>←</span>\r\n              <span>Back to Marketplace</span>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Loading Skeleton */}\r\n          <div className=\"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 animate-pulse\">\r\n            <div className=\"flex flex-col md:flex-row gap-8\">\r\n              <div className=\"flex-shrink-0\">\r\n                <div className=\"w-32 h-32 bg-gray-200 rounded-full\"></div>\r\n              </div>\r\n              <div className=\"flex-1 space-y-4\">\r\n                <div className=\"h-8 bg-gray-200 rounded w-1/3\"></div>\r\n                <div className=\"h-4 bg-gray-200 rounded w-2/3\"></div>\r\n                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n                  <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n                  <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !expert) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"mb-8\">\r\n            <Link \r\n              href=\"/\" \r\n              className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n            >\r\n              <span>←</span>\r\n              <span>Back to Marketplace</span>\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto\">\r\n              <div className=\"text-red-600 text-6xl mb-4\">⚠️</div>\r\n              <h3 className=\"text-xl font-semibold text-red-800 mb-2\">Expert Not Found</h3>\r\n              <p className=\"text-red-600 mb-4\">{error || 'This expert may not exist or is not publicly available.'}</p>\r\n              <Link \r\n                href=\"/\"\r\n                className=\"inline-block px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\r\n              >\r\n                Return to Marketplace\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n        {/* Navigation */}\r\n        <div className=\"mb-8\">\r\n          <Link \r\n            href=\"/\" \r\n            className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n          >\r\n            <span>←</span>\r\n            <span>Back to Marketplace</span>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Expert Profile */}\r\n        <div className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden\">\r\n          {/* Header Section */}\r\n          <div className=\"bg-gradient-to-r from-blue-900 to-indigo-900 p-8 text-white\">\r\n            <div className=\"flex flex-col md:flex-row items-center gap-6\">\r\n              <div className=\"relative\">\r\n                {expert.imageUrl ? (\r\n                  <img \r\n                    src={`http://localhost:3001${expert.imageUrl}`} \r\n                    alt={expert.name}\r\n                    className=\"w-32 h-32 object-cover rounded-full border-4 border-white shadow-xl\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-32 h-32 rounded-full bg-white/20 backdrop-blur-sm border-4 border-white shadow-xl flex items-center justify-center text-6xl\">\r\n                    {getExpertIcon(expert.labels)}\r\n                  </div>\r\n                )}\r\n                <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center\">\r\n                  <div className=\"w-3 h-3 bg-white rounded-full\"></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex-1 text-center md:text-left\">\r\n                <h1 className=\"text-4xl font-bold mb-2\">{expert.name}</h1>\r\n                <div className=\"flex flex-wrap gap-2 justify-center md:justify-start mb-4\">\r\n                  <span className=\"px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium\">\r\n                    {expert.model}\r\n                  </span>\r\n                  <span className=\"px-3 py-1 bg-green-500 rounded-full text-sm font-medium\">\r\n                    ● Online\r\n                  </span>\r\n                </div>\r\n                <p className=\"text-xl text-blue-100 leading-relaxed\">\r\n                  {expert.description}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"p-8\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n              {/* Main Content */}\r\n              <div className=\"lg:col-span-2 space-y-6\">\r\n                {/* About Section */}\r\n                <div>\r\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">About This Expert</h2>\r\n                  <div className=\"bg-gray-50 rounded-xl p-6\">\r\n                    <p className=\"text-gray-700 leading-relaxed whitespace-pre-wrap\">\r\n                      {expert.systemPrompt}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Expertise Areas */}\r\n                {expert.labels && expert.labels.length > 0 && (\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Areas of Expertise</h2>\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                      {expert.labels.map((label, index) => (\r\n                        <span\r\n                          key={index}\r\n                          className=\"px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-medium hover:bg-blue-200 transition-colors\"\r\n                        >\r\n                          #{label}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Additional Info */}\r\n                <div>\r\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Expert Details</h2>\r\n                  <div className=\"bg-gray-50 rounded-xl p-6 space-y-3\">\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-gray-600\">AI Model:</span>\r\n                      <span className=\"font-semibold\">{expert.model}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-gray-600\">Response Time:</span>\r\n                      <span className=\"font-semibold text-green-600\">Instant</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-gray-600\">Availability:</span>\r\n                      <span className=\"font-semibold text-green-600\">24/7</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-gray-600\">Added:</span>\r\n                      <span className=\"font-semibold\">{formatDate(expert.createdAt)}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Sidebar */}\r\n              <div className=\"space-y-6\">\r\n                {/* Action Card */}\r\n                <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Start Conversation</h3>\r\n                  <p className=\"text-gray-600 mb-6\">\r\n                    Ready to get expert assistance? Start chatting now for instant responses.\r\n                  </p>\r\n                  \r\n                  <button\r\n                    onClick={startChat}\r\n                    className=\"w-full py-4 px-6 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 text-lg\"\r\n                    style={{ backgroundColor: '#1E3A8A' }}\r\n                  >\r\n                    🚀 Start Chat\r\n                  </button>\r\n\r\n                  <div className=\"mt-4 pt-4 border-t border-blue-200\">\r\n                    <div className=\"text-center\">\r\n                      <span className=\"text-sm text-gray-600\">\r\n                        💰 Usage fee: {expert.pricingPercentage}% of tokens\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Features */}\r\n                <div className=\"bg-white rounded-2xl p-6 border border-gray-200\">\r\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-4\">What You Get</h3>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-green-600 text-sm\">✓</span>\r\n                      </div>\r\n                      <span className=\"text-gray-700\">Instant responses</span>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-green-600 text-sm\">✓</span>\r\n                      </div>\r\n                      <span className=\"text-gray-700\">Specialized knowledge</span>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-green-600 text-sm\">✓</span>\r\n                      </div>\r\n                      <span className=\"text-gray-700\">24/7 availability</span>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-green-600 text-sm\">✓</span>\r\n                      </div>\r\n                      <span className=\"text-gray-700\">Secure conversations</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpertProfile;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA0BA,MAAM,gBAA8C;QAAC,EAAE,QAAQ,EAAE;;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,SAAS,CAAC;YAEnC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,MAAM;YACzB,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAC;QACrB,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,cAAc,OAAO;QACxE,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,gBAAgB,OAAO;QACtE,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,WAAW,OAAO;QACrE,IAAI,OAAO,QAAQ,CAAC,gBAAgB,OAAO,QAAQ,CAAC,aAAa,OAAO;QACxE,IAAI,OAAO,QAAQ,CAAC,aAAa,OAAO,QAAQ,CAAC,YAAY,OAAO;QACpE,IAAI,OAAO,QAAQ,CAAC,cAAc,OAAO,QAAQ,CAAC,UAAU,OAAO;QACnE,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,YAAY;QAChB,OAAO,IAAI,CAAC,AAAC,kBAA0B,OAAT;IAChC;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/B;IAEA,IAAI,SAAS,CAAC,QAAQ;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAIV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;8CAC5C,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6LAAC;oCAAE,WAAU;8CAAqB,SAAS;;;;;;8CAC3C,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,QAAQ,iBACd,6LAAC;gDACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;gDAC5C,KAAK,OAAO,IAAI;gDAChB,WAAU;;;;;yGAGZ,6LAAC;gDAAI,WAAU;0DACZ,cAAc,OAAO,MAAM;;;;;;0DAGhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2B,OAAO,IAAI;;;;;;0DACpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAK,WAAU;kEAA0D;;;;;;;;;;;;0DAI5E,6LAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;sCAO3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;sEACV,OAAO,YAAY;;;;;;;;;;;;;;;;;4CAMzB,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,mBACvC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;gEAEC,WAAU;;oEACX;oEACG;;+DAHG;;;;;;;;;;;;;;;;0DAWf,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAAiB,OAAO,KAAK;;;;;;;;;;;;0EAE/C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;;;;;;;0EAEjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;;;;;;;0EAEjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAAiB,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpE,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAIlC,6LAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAU;kEACrC;;;;;;kEAID,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;;oEAAwB;oEACvB,OAAO,iBAAiB;oEAAC;;;;;;;;;;;;;;;;;;;;;;;0DAOhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAyB;;;;;;;;;;;kFAE3C,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;0EAElC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAyB;;;;;;;;;;;kFAE3C,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;0EAElC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAyB;;;;;;;;;;;kFAE3C,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;0EAElC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAyB;;;;;;;;;;;kFAE3C,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GApSM;;QAIW,qIAAA,CAAA,YAAS;;;KAJpB;uCAsSS", "debugId": null}}]}