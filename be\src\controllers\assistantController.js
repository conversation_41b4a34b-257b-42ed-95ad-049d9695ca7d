const assistantService = require('../services/assistantService');
const openai = require('../config/openai');

class AssistantController {
  async createAssistant(req, res) {
    try {
      const { name, instructions, model, userId } = req.body;
      const file = req.file;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Validate required fields
      const validationErrors = assistantService.validateAssistantData({
        name,
        instructions,
        model
      });

      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      // Validate file if provided
      if (file && !assistantService.isValidFileType(file.mimetype, file.originalname)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid file type',
          details: ['Supported formats: PDF, TXT, DOCX, DOC, MD, JSON']
        });
      }

      // Process assistant creation
      const result = await assistantService.processAssistantCreation({
        name: name.trim(),
        instructions: instructions.trim(),
        model: model || 'gpt-4-turbo-preview',
        userId: userId
      }, file);

      res.json(result);

    } catch (error) {
      console.error('Create assistant error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async listAssistants(req, res) {
    try {
      const { userId } = req.query;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const assistants = await assistantService.listUserAssistants(userId);

      res.json(assistants);
    } catch (error) {
      console.error('List assistants error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list assistants',
        message: error.message
      });
    }
  }

  async getAssistant(req, res) {
    try {
      const { assistantId } = req.params;
      const assistant = await openai.beta.assistants.retrieve(assistantId);

      res.json({
        success: true,
        assistant: {
          id: assistant.id,
          name: assistant.name,
          instructions: assistant.instructions,
          model: assistant.model,
          created_at: assistant.created_at,
          tools: assistant.tools,
          file_ids: assistant.file_ids || []
        }
      });
    } catch (error) {
      console.error('Get assistant error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get assistant',
        message: error.message
      });
    }
  }
}

module.exports = new AssistantController();