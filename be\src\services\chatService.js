const openai = require('../config/openai');

class ChatService {
  // Clean response by removing citation patterns like 【1:1†source†L1-L5】
  cleanResponse(response) {
    return response.replace(/【\d+:\d+†.*?†L\d+-L\d+】/g, '');
  }

  // Calculate cost based on tokens (rough estimate)
  calculateCost(tokens) {
    // GPT-4 pricing: ~$0.03 per 1K tokens (average of input/output)
    return (tokens / 1000) * 0.03;
  }

  async createThread() {
    const thread = await openai.beta.threads.create();
    return thread.id;
  }

  async addMessageToThread(threadId, message) {
    await openai.beta.threads.messages.create(threadId, {
      role: 'user',
      content: message,
    });
  }

  async runAssistant(threadId, assistantId = null) {
    const run = await openai.beta.threads.runs.create(threadId, {
      assistant_id: assistantId || process.env.ASSISTANT_ID,
    });
    return run;
  }

  async waitForCompletion(threadId, runId) {
    let runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    
    while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    }
    
    return runStatus;
  }

  async getLastMessage(threadId) {
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data[0];
  }

  async getThreadMessages(threadId, userId) {
    // TODO: Add user validation logic here
    // For now, we'll just log the userId for tracking
    console.log(`Getting messages for thread ${threadId} by user ${userId}`);
    
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data.reverse().map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.role === 'assistant' ? this.cleanResponse(msg.content[0].text.value) : msg.content[0].text.value,
      created_at: msg.created_at
    }));
  }

  async processChat(message, threadId = null, userId = null, expertContext = null) {
    try {
      // Create thread if not provided (for OpenAI Assistant API)
      let openaiThreadId = threadId;
      let isNewThread = false;
      
      if (!openaiThreadId) {
        openaiThreadId = await this.createThread();
        isNewThread = true;
      } else {
        // Check if OpenAI thread exists
        try {
          await openai.beta.threads.retrieve(openaiThreadId);
        } catch (error) {
          if (error.status === 404) {
            // OpenAI thread doesn't exist, create new one
            openaiThreadId = await this.createThread();
            isNewThread = true;
          } else {
            throw error;
          }
        }
      }

      // Use expert's assistant ID if provided, otherwise use default
      let assistantId = process.env.ASSISTANT_ID;
      if (expertContext && expertContext.assistantId) {
        assistantId = expertContext.assistantId;
      }

      // Add message to thread
      await this.addMessageToThread(openaiThreadId, message);

      // Run the assistant
      const run = await this.runAssistant(openaiThreadId, assistantId);

      // Wait for completion
      const runStatus = await this.waitForCompletion(openaiThreadId, run.id);

      if (runStatus.status === 'completed') {
        const lastMessage = await this.getLastMessage(openaiThreadId);
        const rawResponse = lastMessage.content[0].text.value;
        const cleanedResponse = this.cleanResponse(rawResponse);
        
        return {
          success: true,
          response: cleanedResponse,
          threadId: openaiThreadId,
          isNewThread: isNewThread,
          tokensUsed: runStatus.usage?.total_tokens || 0,
          cost: this.calculateCost(runStatus.usage?.total_tokens || 0),
          status: 'success',
          usage: {
            prompt_tokens: runStatus.usage?.prompt_tokens || 0,
            completion_tokens: runStatus.usage?.completion_tokens || 0,
            total_tokens: runStatus.usage?.total_tokens || 0
          }
        };
      } else {
        throw new Error(`Assistant run failed with status: ${runStatus.status}`);
      }
    } catch (error) {
      console.error('ChatService.processChat error:', error);
      return {
        success: false,
        error: error.message || 'Failed to process chat'
      };
    }
  }
}

module.exports = new ChatService();