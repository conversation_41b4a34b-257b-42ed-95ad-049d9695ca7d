const { pool } = require('../config/database');

const authenticateToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.headers.token;
    
    if (!token) {
      return res.status(401).json({ 
        error: 'Access token is required',
        message: 'Please provide a valid token in the Authorization header or token header'
      });
    }

    // Check token in database
    const [rows] = await pool.execute(
      'SELECT user_id, nama FROM user WHERE token = ?',
      [token]
    );

    if (rows.length === 0) {
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'The provided token is not valid or has expired'
      });
    }

    // Add user info to request object
    req.user = {
      user_id: rows[0].user_id,
      nama: rows[0].nama,
      token: token
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ 
      error: 'Authentication failed',
      message: 'Internal server error during authentication'
    });
  }
};

module.exports = { authenticateToken };