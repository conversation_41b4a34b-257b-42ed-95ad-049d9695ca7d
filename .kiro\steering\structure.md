# Project Structure

## Root Directory Organization

```
├── be/                          # Backend Express.js application
├── fe/                          # Frontend Next.js application  
├── api-doc/                     # API documentation and examples
├── *.md                         # Project documentation files
└── test-*.js                    # Integration test scripts
```

## Backend Structure (be/)

```
be/
├── src/
│   ├── config/                  # Configuration files
│   │   ├── database.js          # Database connection setup
│   │   ├── openai.js           # OpenAI client configuration
│   │   └── init-db.sql         # Database schema initialization
│   ├── controllers/             # Request handlers (HTTP layer)
│   │   ├── chatController.js    # Chat-related endpoints
│   │   ├── assistantController.js # Assistant management
│   │   └── expertController.js  # Expert management
│   ├── services/                # Business logic layer
│   │   ├── chatService.js       # Chat processing logic
│   │   ├── assistantService.js  # Assistant operations
│   │   └── expertService.js     # Expert operations
│   ├── routes/                  # API route definitions
│   │   ├── chatRoutes.js        # Chat endpoints
│   │   ├── assistantRoutes.js   # Assistant endpoints
│   │   └── expertRoutes.js      # Expert endpoints
│   ├── middleware/              # Express middleware
│   │   ├── cors.js              # CORS configuration
│   │   ├── errorHandler.js      # Global error handling
│   │   └── auth.js              # Authentication middleware
│   ├── utils/                   # Utility functions
│   │   └── initDatabase.js      # Database initialization
│   └── app.js                   # Express app configuration
├── uploads/                     # File upload storage
├── server.js                    # Server entry point
├── package.json                 # Dependencies and scripts
└── .env                         # Environment variables
```

## Frontend Structure (fe/)

```
fe/
├── src/
│   ├── app/                     # Next.js App Router pages
│   │   ├── api/                 # API route handlers (proxy)
│   │   ├── experts/             # Expert management pages
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # Root layout component
│   │   └── page.tsx             # Home page
│   ├── components/              # Reusable React components
│   │   ├── ui/                  # Base UI components (shadcn/ui)
│   │   ├── CreateExpert.tsx     # Expert creation form
│   │   ├── ExpertList.tsx       # Expert listing component
│   │   ├── ExpertPanel.tsx      # Expert management panel
│   │   └── Navigation.tsx       # Site navigation
│   └── lib/                     # Utility libraries
│       ├── api.ts               # API client functions
│       └── utils.ts             # General utilities
├── public/                      # Static assets
├── package.json                 # Dependencies and scripts
├── next.config.ts               # Next.js configuration
├── tsconfig.json                # TypeScript configuration
├── components.json              # shadcn/ui configuration
└── .env.local                   # Environment variables
```

## Key Architectural Patterns

### Backend Patterns
- **Layered Architecture**: Routes → Controllers → Services → Database
- **Middleware Chain**: CORS → JSON parsing → Authentication → Routes → Error handling
- **Configuration Separation**: Environment-specific configs in dedicated files
- **Database Abstraction**: Service layer handles all database operations

### Frontend Patterns
- **App Router**: File-based routing with app/ directory
- **Component Composition**: Reusable components with clear separation of concerns
- **API Abstraction**: Centralized API calls in lib/api.ts
- **Type Safety**: Full TypeScript coverage with strict mode

### File Naming Conventions
- **Backend**: camelCase for files (chatController.js, expertService.js)
- **Frontend**: PascalCase for components (CreateExpert.tsx), camelCase for utilities
- **Routes**: RESTful naming (/api/experts, /api/chat)
- **Database**: snake_case for table/column names

### Import/Export Patterns
- **Backend**: CommonJS (require/module.exports)
- **Frontend**: ES modules (import/export)
- **Path Aliases**: @/* for src/ directory in frontend