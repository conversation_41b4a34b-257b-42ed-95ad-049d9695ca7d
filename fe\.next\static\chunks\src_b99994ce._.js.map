{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/api.ts"], "sourcesContent": ["const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\ninterface ApiOptions {\r\n    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\r\n    body?: any;\r\n    headers?: Record<string, string>;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n    const { method = 'GET', body, headers = {} } = options;\r\n\r\n    const fullUrl = `${API_URL}${endpoint}`;\r\n\r\n    // Debug logging\r\n    console.log('🔍 API Call Debug:', {\r\n        endpoint,\r\n        fullUrl,\r\n        API_URL,\r\n        TOKEN: TOKEN.substring(0, 3) + '***',\r\n        method,\r\n        body,\r\n        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,\r\n        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN\r\n    });\r\n\r\n    const config: RequestInit = {\r\n        method,\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${TOKEN}`,\r\n            ...headers,\r\n        },\r\n    };\r\n\r\n    if (body && method !== 'GET') {\r\n        config.body = JSON.stringify(body);\r\n    }\r\n\r\n    try {\r\n        const response = await fetch(fullUrl, config);\r\n\r\n        console.log('📡 Response status:', response.status, response.statusText);\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            console.error('❌ API Error:', errorData);\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('✅ API Success:', data);\r\n        return data;\r\n    } catch (error) {\r\n        console.error('💥 API call failed:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n    // Health check (no token required)\r\n    health: () => apiCall('/health'),\r\n\r\n    // Chat endpoints (token required)\r\n    chat: (message: string, threadId?: string, expertId?: string, expertContext?: any) => apiCall('/api/chat', {\r\n        method: 'POST',\r\n        body: { message, threadId, expertId, expertContext }\r\n    }),\r\n\r\n    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n    // Chat session endpoints\r\n    getUserChatSessions: (limit?: number) => apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ''}`),\r\n\r\n    updateSessionTitle: (sessionId: string, title: string) => apiCall(`/api/chat/sessions/${sessionId}/title`, {\r\n        method: 'PUT',\r\n        body: { title }\r\n    }),\r\n\r\n    deleteSession: (sessionId: string) => apiCall(`/api/chat/sessions/${sessionId}`, {\r\n        method: 'DELETE'\r\n    }),\r\n\r\n    // Assistant endpoints (token required)\r\n    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),\r\n\r\n    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {\r\n        method: 'POST',\r\n        body: { threadId, message }\r\n    }),\r\n\r\n    runAssistant: (threadId: string) => apiCall('/assistant/run', {\r\n        method: 'POST',\r\n        body: { threadId }\r\n    }),\r\n\r\n    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n    // Expert endpoints (token required)\r\n    createExpert: (expertData: FormData) => {\r\n        return fetch(`${API_URL}/api/experts`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: expertData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n\r\n    listExperts: () => apiCall('/api/experts'),\r\n\r\n    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {\r\n        const formData = new FormData();\r\n        \r\n        // Add text fields\r\n        Object.keys(expertData).forEach(key => {\r\n            if (expertData[key] !== undefined && expertData[key] !== null) {\r\n                if (key === 'labels' && Array.isArray(expertData[key])) {\r\n                    formData.append(key, JSON.stringify(expertData[key]));\r\n                } else {\r\n                    formData.append(key, expertData[key].toString());\r\n                }\r\n            }\r\n        });\r\n\r\n        // Add files\r\n        if (knowledgeBaseFile) {\r\n            formData.append('file', knowledgeBaseFile);\r\n        }\r\n        if (imageFile) {\r\n            formData.append('image', imageFile);\r\n        }\r\n\r\n        return fetch(`${API_URL}/api/experts/${expertId}`, {\r\n            method: 'PUT',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: formData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n};"], "names": [], "mappings": ";;;;AAAgB;AAAhB,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAQxC,eAAe,QAAQ,QAAgB;QAAE,UAAA,iEAAsB,CAAC;IACnE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG;IAE/C,MAAM,UAAU,AAAC,GAAY,OAAV,SAAmB,OAAT;IAE7B,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAC9B;QACA;QACA;QACA,OAAO,MAAM,SAAS,CAAC,GAAG,KAAK;QAC/B;QACA;QACA,iCAAiC;QACjC,+BAA+B;IACnC;IAEA,MAAM,SAAsB;QACxB;QACA,SAAS;YACL,gBAAgB;YAChB,iBAAiB,AAAC,UAAe,OAAN;YAC3B,GAAG,OAAO;QACd;IACJ;IAEA,IAAI,QAAQ,WAAW,OAAO;QAC1B,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC;IACjC;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;QAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACV;AACJ;AAGO,MAAM,MAAM;IACf,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CAAC,SAAiB,UAAmB,UAAmB,gBAAwB,QAAQ,aAAa;YACvG,QAAQ;YACR,MAAM;gBAAE;gBAAS;gBAAU;gBAAU;YAAc;QACvD;IAEA,mBAAmB,CAAC,WAAqB,QAAQ,AAAC,eAAuB,OAAT,UAAS;IAEzE,yBAAyB;IACzB,qBAAqB,CAAC,QAAmB,QAAQ,AAAC,qBAAmD,OAA/B,QAAQ,AAAC,UAAe,OAAN,SAAU;IAElG,oBAAoB,CAAC,WAAmB,QAAkB,QAAQ,AAAC,sBAA+B,OAAV,WAAU,WAAS;YACvG,QAAQ;YACR,MAAM;gBAAE;YAAM;QAClB;IAEA,eAAe,CAAC,YAAsB,QAAQ,AAAC,sBAA+B,OAAV,YAAa;YAC7E,QAAQ;QACZ;IAEA,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAAoB,QAAQ,sBAAsB;YAC9E,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC9B;IAEA,cAAc,CAAC,WAAqB,QAAQ,kBAAkB;YAC1D,QAAQ;YACR,MAAM;gBAAE;YAAS;QACrB;IAEA,aAAa,CAAC,WAAqB,QAAQ,AAAC,uBAA+B,OAAT;IAElE,oCAAoC;IACpC,cAAc,CAAC;QACX,OAAO,MAAM,AAAC,GAAU,OAAR,SAAQ,iBAAe;YACnC,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;IAEA,aAAa,IAAM,QAAQ;IAE3B,WAAW,CAAC,WAAqB,QAAQ,AAAC,gBAAwB,OAAT;IAEzD,cAAc,CAAC,UAAkB,YAAiB,mBAAiC;QAC/E,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC3D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACpD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACvD,OAAO;oBACH,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBACjD;YACJ;QACJ;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACnB,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,IAAI,WAAW;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,OAAO,MAAM,AAAC,GAAyB,OAAvB,SAAQ,iBAAwB,OAAT,WAAY;YAC/C,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/CreateExpert.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { api } from '@/lib/api';\r\n\r\ninterface CreateExpertProps {\r\n  onExpertCreated?: (expert: any) => void;\r\n  onCancel?: () => void;\r\n}\r\n\r\nconst CreateExpert: React.FC<CreateExpertProps> = ({ onExpertCreated, onCancel }) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    description: '',\r\n    systemPrompt: '',\r\n    model: 'gpt-4o-mini',\r\n    pricingPercentage: '0.00',\r\n    isPublic: false\r\n  });\r\n  const [labels, setLabels] = useState<string[]>([]);\r\n  const [currentLabel, setCurrentLabel] = useState('');\r\n  const [knowledgeBaseFile, setKnowledgeBaseFile] = useState<File | null>(null);\r\n  const [imageFile, setImageFile] = useState<File | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const models = [\r\n    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },\r\n    { value: 'gpt-4', label: 'GPT-4' },\r\n    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },\r\n    { value: 'gpt-4o', label: 'GPT-4o' },\r\n    { value: 'gpt-4o-mini', label: 'GPT-4o Mini' }\r\n  ];\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value, type, checked } = e.target as HTMLInputElement;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleAddLabel = () => {\r\n    if (currentLabel.trim() && labels.length < 5 && !labels.includes(currentLabel.trim())) {\r\n      setLabels(prev => [...prev, currentLabel.trim()]);\r\n      setCurrentLabel('');\r\n    }\r\n  };\r\n\r\n  const handleRemoveLabel = (indexToRemove: number) => {\r\n    setLabels(prev => prev.filter((_, index) => index !== indexToRemove));\r\n  };\r\n\r\n  const handleLabelKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      handleAddLabel();\r\n    }\r\n  };\r\n\r\n  const handleKnowledgeBaseFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const selectedFile = e.target.files?.[0];\r\n    if (selectedFile) {\r\n      // Validate file type for knowledge base\r\n      const validTypes = ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/markdown', 'application/json'];\r\n      const validExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];\r\n      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));\r\n      \r\n      if (validTypes.includes(selectedFile.type) || validExtensions.includes(fileExtension)) {\r\n        setKnowledgeBaseFile(selectedFile);\r\n        setError(null);\r\n      } else {\r\n        setError('Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON');\r\n        setKnowledgeBaseFile(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const selectedFile = e.target.files?.[0];\r\n    if (selectedFile) {\r\n      // Validate file type for images\r\n      const validImageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];\r\n      const validImageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];\r\n      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));\r\n      \r\n      // Check file size (10MB limit)\r\n      const maxSize = 10 * 1024 * 1024; // 10MB in bytes\r\n      \r\n      if (selectedFile.size > maxSize) {\r\n        setError('Image file size must be less than 10MB');\r\n        setImageFile(null);\r\n        return;\r\n      }\r\n      \r\n      if (validImageTypes.includes(selectedFile.type) || validImageExtensions.includes(fileExtension)) {\r\n        setImageFile(selectedFile);\r\n        setError(null);\r\n      } else {\r\n        setError('Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP');\r\n        setImageFile(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const formDataToSend = new FormData();\r\n      formDataToSend.append('name', formData.name);\r\n      formDataToSend.append('description', formData.description);\r\n      formDataToSend.append('systemPrompt', formData.systemPrompt);\r\n      formDataToSend.append('model', formData.model);\r\n      formDataToSend.append('pricingPercentage', formData.pricingPercentage);\r\n      formDataToSend.append('isPublic', formData.isPublic.toString());\r\n      formDataToSend.append('labels', JSON.stringify(labels));\r\n      \r\n      if (knowledgeBaseFile) {\r\n        formDataToSend.append('file', knowledgeBaseFile);\r\n      }\r\n      \r\n      if (imageFile) {\r\n        formDataToSend.append('image', imageFile);\r\n      }\r\n\r\n      const result = await api.createExpert(formDataToSend);\r\n      \r\n      if (result.success) {\r\n        onExpertCreated?.(result.expert);\r\n        // Reset form\r\n        setFormData({\r\n          name: '',\r\n          description: '',\r\n          systemPrompt: '',\r\n          model: 'gpt-4o-mini',\r\n          pricingPercentage: '0.00',\r\n          isPublic: false\r\n        });\r\n        setLabels([]);\r\n        setCurrentLabel('');\r\n        setKnowledgeBaseFile(null);\r\n        setImageFile(null);\r\n      } else {\r\n        setError(result.error || 'Failed to create expert');\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to create expert');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\r\n      <h2 className=\"text-2xl font-bold text-center mb-6\">Create AI Expert</h2>\r\n      <p className=\"text-gray-600 text-center mb-8\">Fill in the details to create a new AI expert profile.</p>\r\n\r\n      {error && (\r\n        <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {/* Name */}\r\n        <div>\r\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Name\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"name\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleInputChange}\r\n            placeholder=\"e.g., Creative Writer\"\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <div>\r\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Description\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleInputChange}\r\n            placeholder=\"Describe the expert's capabilities and purpose.\"\r\n            rows={4}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        {/* Image Upload */}\r\n        <div>\r\n          <label htmlFor=\"imageFile\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Upload Image (Optional)\r\n          </label>\r\n          <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\r\n            <input\r\n              type=\"file\"\r\n              id=\"imageFile\"\r\n              onChange={handleImageFileChange}\r\n              accept=\".png,.jpg,.jpeg,.gif,.webp\"\r\n              className=\"hidden\"\r\n            />\r\n            <label htmlFor=\"imageFile\" className=\"cursor-pointer\">\r\n              <div className=\"text-gray-400 mb-2\">\r\n                <svg className=\"mx-auto h-12 w-12\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\r\n                  <path d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20v-2a2 2 0 012-2h8a2 2 0 012 2v2m0 0v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8m8-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v2m0 0h12m0 0v8a2 2 0 01-2 2h-8a2 2 0 01-2-2v-8z\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                </svg>\r\n              </div>\r\n              <p className=\"text-blue-600 hover:text-blue-500\">Upload an image or drag and drop</p>\r\n              <p className=\"text-sm text-gray-500\">PNG, JPG, GIF, WEBP up to 10MB</p>\r\n            </label>\r\n            {imageFile && (\r\n              <p className=\"mt-2 text-sm text-green-600\">Selected: {imageFile.name}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Knowledge Base Upload */}\r\n        <div>\r\n          <label htmlFor=\"knowledgeBaseFile\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Upload Knowledge Base (Optional)\r\n          </label>\r\n          <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\r\n            <input\r\n              type=\"file\"\r\n              id=\"knowledgeBaseFile\"\r\n              onChange={handleKnowledgeBaseFileChange}\r\n              accept=\".pdf,.txt,.docx,.doc,.md,.json\"\r\n              className=\"hidden\"\r\n            />\r\n            <label htmlFor=\"knowledgeBaseFile\" className=\"cursor-pointer\">\r\n              <div className=\"text-gray-400 mb-2\">\r\n                <svg className=\"mx-auto h-12 w-12\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\r\n                  <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                </svg>\r\n              </div>\r\n              <p className=\"text-blue-600 hover:text-blue-500\">Upload a knowledge base file or drag and drop</p>\r\n              <p className=\"text-sm text-gray-500\">PDF, TXT, DOCX, DOC, MD, JSON</p>\r\n            </label>\r\n            {knowledgeBaseFile && (\r\n              <p className=\"mt-2 text-sm text-green-600\">Selected: {knowledgeBaseFile.name}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* System Prompt */}\r\n        <div>\r\n          <label htmlFor=\"systemPrompt\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            System Prompt\r\n          </label>\r\n          <textarea\r\n            id=\"systemPrompt\"\r\n            name=\"systemPrompt\"\r\n            value={formData.systemPrompt}\r\n            onChange={handleInputChange}\r\n            placeholder=\"Enter the system prompt that defines the expert's behavior.\"\r\n            rows={6}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        {/* Model Selection */}\r\n        <div>\r\n          <label htmlFor=\"model\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Model Selection\r\n          </label>\r\n          <select\r\n            id=\"model\"\r\n            name=\"model\"\r\n            value={formData.model}\r\n            onChange={handleInputChange}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          >\r\n            {models.map(model => (\r\n              <option key={model.value} value={model.value}>\r\n                {model.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Pricing */}\r\n        <div>\r\n          <label htmlFor=\"pricingPercentage\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Pricing (% of token usage)\r\n          </label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"number\"\r\n              id=\"pricingPercentage\"\r\n              name=\"pricingPercentage\"\r\n              value={formData.pricingPercentage}\r\n              onChange={handleInputChange}\r\n              min=\"0\"\r\n              max=\"100\"\r\n              step=\"0.01\"\r\n              className=\"w-full px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <span className=\"absolute right-3 top-2 text-gray-500\">%</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Visibility */}\r\n        <div>\r\n          <label className=\"flex items-center space-x-3\">\r\n            <input\r\n              type=\"checkbox\"\r\n              name=\"isPublic\"\r\n              checked={formData.isPublic}\r\n              onChange={handleInputChange}\r\n              className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2\"\r\n            />\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              Make this expert public (others can discover and use it)\r\n            </span>\r\n          </label>\r\n          <p className=\"text-xs text-gray-500 mt-1 ml-7\">\r\n            {formData.isPublic ? 'This expert will be visible to all users' : 'This expert will be private (unlisted)'}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Labels */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Labels (max 5)\r\n          </label>\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex space-x-2\">\r\n              <input\r\n                type=\"text\"\r\n                value={currentLabel}\r\n                onChange={(e) => setCurrentLabel(e.target.value)}\r\n                onKeyPress={handleLabelKeyPress}\r\n                placeholder=\"Add a label...\"\r\n                maxLength={50}\r\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                disabled={labels.length >= 5}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleAddLabel}\r\n                disabled={!currentLabel.trim() || labels.length >= 5 || labels.includes(currentLabel.trim())}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Add\r\n              </button>\r\n            </div>\r\n            \r\n            {labels.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {labels.map((label, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\"\r\n                  >\r\n                    {label}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleRemoveLabel(index)}\r\n                      className=\"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            <p className=\"text-xs text-gray-500\">\r\n              {labels.length}/5 labels used. Labels help users discover your expert.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Buttons */}\r\n        <div className=\"flex justify-end space-x-4\">\r\n          {onCancel && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onCancel}\r\n              className=\"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          )}\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isLoading}\r\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {isLoading ? 'Creating...' : 'Create Expert'}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CreateExpert;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,eAA4C;QAAC,EAAE,eAAe,EAAE,QAAQ,EAAE;;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,cAAc;QACd,OAAO;QACP,mBAAmB;QACnB,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS;QACb;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAe,OAAO;QAAc;KAC9C;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa,IAAI,MAAM,OAAO,MAAM,GAAG,KAAK,CAAC,OAAO,QAAQ,CAAC,aAAa,IAAI,KAAK;YACrF,UAAU,CAAA,OAAQ;uBAAI;oBAAM,aAAa,IAAI;iBAAG;YAChD,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU;IACxD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gCAAgC,CAAC;YAChB;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,wCAAwC;YACxC,MAAM,aAAa;gBAAC;gBAAmB;gBAAc;gBAA2E;gBAAsB;gBAAiB;aAAmB;YAC1L,MAAM,kBAAkB;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAO;aAAQ;YACzE,MAAM,gBAAgB,aAAa,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC;YAE9F,IAAI,WAAW,QAAQ,CAAC,aAAa,IAAI,KAAK,gBAAgB,QAAQ,CAAC,gBAAgB;gBACrF,qBAAqB;gBACrB,SAAS;YACX,OAAO;gBACL,SAAS;gBACT,qBAAqB;YACvB;QACF;IACF;IAEA,MAAM,wBAAwB,CAAC;YACR;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,gCAAgC;YAChC,MAAM,kBAAkB;gBAAC;gBAAa;gBAAc;gBAAa;gBAAa;aAAa;YAC3F,MAAM,uBAAuB;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;aAAQ;YACvE,MAAM,gBAAgB,aAAa,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC;YAE9F,+BAA+B;YAC/B,MAAM,UAAU,KAAK,OAAO,MAAM,gBAAgB;YAElD,IAAI,aAAa,IAAI,GAAG,SAAS;gBAC/B,SAAS;gBACT,aAAa;gBACb;YACF;YAEA,IAAI,gBAAgB,QAAQ,CAAC,aAAa,IAAI,KAAK,qBAAqB,QAAQ,CAAC,gBAAgB;gBAC/F,aAAa;gBACb,SAAS;YACX,OAAO;gBACL,SAAS;gBACT,aAAa;YACf;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,QAAQ,SAAS,IAAI;YAC3C,eAAe,MAAM,CAAC,eAAe,SAAS,WAAW;YACzD,eAAe,MAAM,CAAC,gBAAgB,SAAS,YAAY;YAC3D,eAAe,MAAM,CAAC,SAAS,SAAS,KAAK;YAC7C,eAAe,MAAM,CAAC,qBAAqB,SAAS,iBAAiB;YACrE,eAAe,MAAM,CAAC,YAAY,SAAS,QAAQ,CAAC,QAAQ;YAC5D,eAAe,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC;YAE/C,IAAI,mBAAmB;gBACrB,eAAe,MAAM,CAAC,QAAQ;YAChC;YAEA,IAAI,WAAW;gBACb,eAAe,MAAM,CAAC,SAAS;YACjC;YAEA,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,YAAY,CAAC;YAEtC,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAAA,sCAAA,gBAAkB,OAAO,MAAM;gBAC/B,aAAa;gBACb,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,cAAc;oBACd,OAAO;oBACP,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,UAAU,EAAE;gBACZ,gBAAgB;gBAChB,qBAAqB;gBACrB,aAAa;YACf,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BACpD,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;YAE7C,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU;gCACV,aAAY;gCACZ,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAKZ,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;kCAKd,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA+C;;;;;;0CAGpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,UAAU;wCACV,QAAO;wCACP,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAoB,QAAO;oDAAe,MAAK;oDAAO,SAAQ;8DAC3E,cAAA,6LAAC;wDAAK,GAAE;wDAAqP,aAAa;wDAAG,eAAc;wDAAQ,gBAAe;;;;;;;;;;;;;;;;0DAGtT,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;oCAEtC,2BACC,6LAAC;wCAAE,WAAU;;4CAA8B;4CAAW,UAAU,IAAI;;;;;;;;;;;;;;;;;;;kCAM1E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAoB,WAAU;0CAA+C;;;;;;0CAG5F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,UAAU;wCACV,QAAO;wCACP,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAoB,WAAU;;0DAC3C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAoB,QAAO;oDAAe,MAAK;oDAAO,SAAQ;8DAC3E,cAAA,6LAAC;wDAAK,GAAE;wDAAyL,aAAa;wDAAG,eAAc;wDAAQ,gBAAe;;;;;;;;;;;;;;;;0DAG1P,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;oCAEtC,mCACC,6LAAC;wCAAE,WAAU;;4CAA8B;4CAAW,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;;kCAMlF,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA+C;;;;;;0CAGvF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAKZ,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAU;0CAET,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;wCAAyB,OAAO,MAAM,KAAK;kDACzC,MAAM,KAAK;uCADD,MAAM,KAAK;;;;;;;;;;;;;;;;kCAQ9B,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAoB,WAAU;0CAA+C;;;;;;0CAG5F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,iBAAiB;wCACjC,UAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;;kCAK3D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,SAAS,SAAS,QAAQ;wCAC1B,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAItD,6LAAC;gCAAE,WAAU;0CACV,SAAS,QAAQ,GAAG,6CAA6C;;;;;;;;;;;;kCAKtE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,aAAY;gDACZ,WAAW;gDACX,WAAU;gDACV,UAAU,OAAO,MAAM,IAAI;;;;;;0DAE7B,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,aAAa,IAAI,MAAM,OAAO,MAAM,IAAI,KAAK,OAAO,QAAQ,CAAC,aAAa,IAAI;gDACzF,WAAU;0DACX;;;;;;;;;;;;oCAKF,OAAO,MAAM,GAAG,mBACf,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;gDAEC,WAAU;;oDAET;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEACX;;;;;;;+CARI;;;;;;;;;;kDAgBb,6LAAC;wCAAE,WAAU;;4CACV,OAAO,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;;4BACZ,0BACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GA9YM;KAAA;uCAgZS", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/EditExpert.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { api } from '@/lib/api';\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\ninterface EditExpertProps {\r\n  expert: Expert;\r\n  onExpertUpdated?: (expert: any) => void;\r\n  onCancel?: () => void;\r\n}\r\n\r\nconst EditExpert: React.FC<EditExpertProps> = ({ expert, onExpertUpdated, onCancel }) => {\r\n  const [formData, setFormData] = useState({\r\n    name: expert.name,\r\n    description: expert.description || '',\r\n    systemPrompt: expert.systemPrompt,\r\n    model: expert.model,\r\n    pricingPercentage: expert.pricingPercentage.toString(),\r\n    isPublic: expert.isPublic || false\r\n  });\r\n  const [labels, setLabels] = useState<string[]>(expert.labels || []);\r\n  const [currentLabel, setCurrentLabel] = useState('');\r\n  const [knowledgeBaseFile, setKnowledgeBaseFile] = useState<File | null>(null);\r\n  const [imageFile, setImageFile] = useState<File | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const models = [\r\n    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },\r\n    { value: 'gpt-4', label: 'GPT-4' },\r\n    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },\r\n    { value: 'gpt-4o', label: 'GPT-4o' },\r\n    { value: 'gpt-4o-mini', label: 'GPT-4o Mini' }\r\n  ];\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value, type, checked } = e.target as HTMLInputElement;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleAddLabel = () => {\r\n    if (currentLabel.trim() && labels.length < 5 && !labels.includes(currentLabel.trim())) {\r\n      setLabels(prev => [...prev, currentLabel.trim()]);\r\n      setCurrentLabel('');\r\n    }\r\n  };\r\n\r\n  const handleRemoveLabel = (indexToRemove: number) => {\r\n    setLabels(prev => prev.filter((_, index) => index !== indexToRemove));\r\n  };\r\n\r\n  const handleLabelKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      handleAddLabel();\r\n    }\r\n  };\r\n\r\n  const handleKnowledgeBaseFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const selectedFile = e.target.files?.[0];\r\n    if (selectedFile) {\r\n      // Validate file type for knowledge base\r\n      const validTypes = ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/markdown', 'application/json'];\r\n      const validExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];\r\n      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));\r\n      \r\n      if (validTypes.includes(selectedFile.type) || validExtensions.includes(fileExtension)) {\r\n        setKnowledgeBaseFile(selectedFile);\r\n        setError(null);\r\n      } else {\r\n        setError('Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON');\r\n        setKnowledgeBaseFile(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const selectedFile = e.target.files?.[0];\r\n    if (selectedFile) {\r\n      // Validate file type for images\r\n      const validImageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];\r\n      const validImageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];\r\n      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));\r\n      \r\n      // Check file size (10MB limit)\r\n      const maxSize = 10 * 1024 * 1024; // 10MB in bytes\r\n      \r\n      if (selectedFile.size > maxSize) {\r\n        setError('Image file size must be less than 10MB');\r\n        setImageFile(null);\r\n        return;\r\n      }\r\n      \r\n      if (validImageTypes.includes(selectedFile.type) || validImageExtensions.includes(fileExtension)) {\r\n        setImageFile(selectedFile);\r\n        setError(null);\r\n      } else {\r\n        setError('Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP');\r\n        setImageFile(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    // Validate required fields\r\n    if (!formData.name.trim() || !formData.systemPrompt.trim()) {\r\n      setError('Name and system prompt are required');\r\n      return;\r\n    }\r\n\r\n    // Validate pricing percentage\r\n    const pricingPercent = parseFloat(formData.pricingPercentage);\r\n    if (isNaN(pricingPercent) || pricingPercent < 0 || pricingPercent > 100) {\r\n      setError('Pricing percentage must be between 0 and 100');\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const result = await api.updateExpert(expert.id, {\r\n        name: formData.name.trim(),\r\n        description: formData.description.trim(),\r\n        systemPrompt: formData.systemPrompt.trim(),\r\n        model: formData.model,\r\n        pricingPercentage: pricingPercent,\r\n        isPublic: formData.isPublic,\r\n        labels: labels\r\n      }, knowledgeBaseFile, imageFile);\r\n\r\n      if (result.success) {\r\n        onExpertUpdated?.(result.expert);\r\n      } else {\r\n        setError(result.error || 'Failed to update expert');\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to update expert');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md\">\r\n      <h2 className=\"text-2xl font-bold mb-6\">Edit AI Expert</h2>\r\n      \r\n      {error && (\r\n        <div className=\"mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {/* Name */}\r\n        <div>\r\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Expert Name *\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"name\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleInputChange}\r\n            required\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            placeholder=\"Enter expert name\"\r\n          />\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <div>\r\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Description\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleInputChange}\r\n            rows={3}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            placeholder=\"Describe what this expert does\"\r\n          />\r\n        </div>\r\n\r\n        {/* System Prompt */}\r\n        <div>\r\n          <label htmlFor=\"systemPrompt\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            System Prompt *\r\n          </label>\r\n          <textarea\r\n            id=\"systemPrompt\"\r\n            name=\"systemPrompt\"\r\n            value={formData.systemPrompt}\r\n            onChange={handleInputChange}\r\n            required\r\n            rows={6}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            placeholder=\"Define the expert's behavior and knowledge\"\r\n          />\r\n        </div>\r\n\r\n        {/* Model and Pricing */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <label htmlFor=\"model\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              AI Model\r\n            </label>\r\n            <select\r\n              id=\"model\"\r\n              name=\"model\"\r\n              value={formData.model}\r\n              onChange={handleInputChange}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            >\r\n              {models.map((model) => (\r\n                <option key={model.value} value={model.value}>\r\n                  {model.label}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          <div>\r\n            <label htmlFor=\"pricingPercentage\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Pricing Percentage (%)\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"pricingPercentage\"\r\n              name=\"pricingPercentage\"\r\n              value={formData.pricingPercentage}\r\n              onChange={handleInputChange}\r\n              min=\"0\"\r\n              max=\"100\"\r\n              step=\"0.01\"\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              placeholder=\"0.00\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Public Toggle */}\r\n        <div>\r\n          <label className=\"flex items-center space-x-2\">\r\n            <input\r\n              type=\"checkbox\"\r\n              name=\"isPublic\"\r\n              checked={formData.isPublic}\r\n              onChange={handleInputChange}\r\n              className=\"rounded border-gray-300 focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <span className=\"text-sm font-medium text-gray-700\">Make this expert public</span>\r\n          </label>\r\n          <p className=\"text-xs text-gray-500 mt-1\">\r\n            Public experts can be discovered and used by other users\r\n          </p>\r\n        </div>\r\n\r\n        {/* File Uploads */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <label htmlFor=\"knowledgeBase\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Knowledge Base File (Optional)\r\n            </label>\r\n            <input\r\n              type=\"file\"\r\n              id=\"knowledgeBase\"\r\n              onChange={handleKnowledgeBaseFileChange}\r\n              accept=\".pdf,.txt,.docx,.doc,.md,.json\"\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Upload a new file to update the knowledge base. Supported: PDF, TXT, DOCX, DOC, MD, JSON\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <label htmlFor=\"image\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Expert Image (Optional)\r\n            </label>\r\n            <input\r\n              type=\"file\"\r\n              id=\"image\"\r\n              onChange={handleImageFileChange}\r\n              accept=\"image/*\"\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Upload a new image to replace the current one. Max 10MB. Supported: PNG, JPG, JPEG, GIF, WEBP\r\n            </p>\r\n            {expert.imageUrl && (\r\n              <div className=\"mt-2\">\r\n                <p className=\"text-xs text-gray-600\">Current image:</p>\r\n                <img \r\n                  src={`http://localhost:3001${expert.imageUrl}`} \r\n                  alt=\"Current expert image\" \r\n                  className=\"w-16 h-16 object-cover rounded border mt-1\"\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Labels */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Labels (Optional)\r\n          </label>\r\n          \r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex space-x-2\">\r\n              <input\r\n                type=\"text\"\r\n                value={currentLabel}\r\n                onChange={(e) => setCurrentLabel(e.target.value)}\r\n                onKeyPress={handleLabelKeyPress}\r\n                maxLength={50}\r\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                placeholder=\"Add a label (max 50 characters)\"\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleAddLabel}\r\n                disabled={!currentLabel.trim() || labels.length >= 5 || labels.includes(currentLabel.trim())}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Add\r\n              </button>\r\n            </div>\r\n            \r\n            {labels.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {labels.map((label, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\"\r\n                  >\r\n                    {label}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleRemoveLabel(index)}\r\n                      className=\"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            <p className=\"text-xs text-gray-500\">\r\n              {labels.length}/5 labels used. Labels help users discover your expert.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Buttons */}\r\n        <div className=\"flex justify-end space-x-4\">\r\n          {onCancel && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onCancel}\r\n              className=\"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          )}\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isLoading}\r\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {isLoading ? 'Updating...' : 'Update Expert'}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EditExpert;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA0BA,MAAM,aAAwC;QAAC,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE;;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,OAAO,IAAI;QACjB,aAAa,OAAO,WAAW,IAAI;QACnC,cAAc,OAAO,YAAY;QACjC,OAAO,OAAO,KAAK;QACnB,mBAAmB,OAAO,iBAAiB,CAAC,QAAQ;QACpD,UAAU,OAAO,QAAQ,IAAI;IAC/B;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,OAAO,MAAM,IAAI,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS;QACb;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAe,OAAO;QAAc;KAC9C;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa,IAAI,MAAM,OAAO,MAAM,GAAG,KAAK,CAAC,OAAO,QAAQ,CAAC,aAAa,IAAI,KAAK;YACrF,UAAU,CAAA,OAAQ;uBAAI;oBAAM,aAAa,IAAI;iBAAG;YAChD,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU;IACxD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gCAAgC,CAAC;YAChB;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,wCAAwC;YACxC,MAAM,aAAa;gBAAC;gBAAmB;gBAAc;gBAA2E;gBAAsB;gBAAiB;aAAmB;YAC1L,MAAM,kBAAkB;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAO;aAAQ;YACzE,MAAM,gBAAgB,aAAa,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC;YAE9F,IAAI,WAAW,QAAQ,CAAC,aAAa,IAAI,KAAK,gBAAgB,QAAQ,CAAC,gBAAgB;gBACrF,qBAAqB;gBACrB,SAAS;YACX,OAAO;gBACL,SAAS;gBACT,qBAAqB;YACvB;QACF;IACF;IAEA,MAAM,wBAAwB,CAAC;YACR;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,gCAAgC;YAChC,MAAM,kBAAkB;gBAAC;gBAAa;gBAAc;gBAAa;gBAAa;aAAa;YAC3F,MAAM,uBAAuB;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;aAAQ;YACvE,MAAM,gBAAgB,aAAa,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC;YAE9F,+BAA+B;YAC/B,MAAM,UAAU,KAAK,OAAO,MAAM,gBAAgB;YAElD,IAAI,aAAa,IAAI,GAAG,SAAS;gBAC/B,SAAS;gBACT,aAAa;gBACb;YACF;YAEA,IAAI,gBAAgB,QAAQ,CAAC,aAAa,IAAI,KAAK,qBAAqB,QAAQ,CAAC,gBAAgB;gBAC/F,aAAa;gBACb,SAAS;YACX,OAAO;gBACL,SAAS;gBACT,aAAa;YACf;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,2BAA2B;QAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YAC1D,SAAS;YACT;QACF;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,WAAW,SAAS,iBAAiB;QAC5D,IAAI,MAAM,mBAAmB,iBAAiB,KAAK,iBAAiB,KAAK;YACvE,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBAC/C,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,cAAc,SAAS,YAAY,CAAC,IAAI;gBACxC,OAAO,SAAS,KAAK;gBACrB,mBAAmB;gBACnB,UAAU,SAAS,QAAQ;gBAC3B,QAAQ;YACV,GAAG,mBAAmB;YAEtB,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAAA,sCAAA,gBAAkB,OAAO,MAAM;YACjC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;YAEvC,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU;gCACV,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA+C;;;;;;0CAGvF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,QAAQ;gCACR,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,WAAU;kDAET,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gDAAyB,OAAO,MAAM,KAAK;0DACzC,MAAM,KAAK;+CADD,MAAM,KAAK;;;;;;;;;;;;;;;;0CAO9B,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAoB,WAAU;kDAA+C;;;;;;kDAG5F,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,iBAAiB;wCACjC,UAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,SAAS,SAAS,QAAQ;wCAC1B,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAgB,WAAU;kDAA+C;;;;;;kDAGxF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,UAAU;wCACV,QAAO;wCACP,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,UAAU;wCACV,QAAO;wCACP,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;oCAGzC,OAAO,QAAQ,kBACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;gDAC5C,KAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAIhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,WAAW;gDACX,WAAU;gDACV,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,aAAa,IAAI,MAAM,OAAO,MAAM,IAAI,KAAK,OAAO,QAAQ,CAAC,aAAa,IAAI;gDACzF,WAAU;0DACX;;;;;;;;;;;;oCAKF,OAAO,MAAM,GAAG,mBACf,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;gDAEC,WAAU;;oDAET;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEACX;;;;;;;+CARI;;;;;;;;;;kDAgBb,6LAAC;wCAAE,WAAU;;4CACV,OAAO,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;;4BACZ,0BACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAvXM;KAAA;uCAyXS", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ExpertList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { api } from '@/lib/api';\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\ninterface ExpertListProps {\r\n  onExpertSelect?: (expert: Expert) => void;\r\n  onExpertEdit?: (expert: Expert) => void;\r\n  refreshTrigger?: number;\r\n}\r\n\r\nconst ExpertList: React.FC<ExpertListProps> = ({ onExpertSelect, onExpertEdit, refreshTrigger }) => {\r\n  const [experts, setExperts] = useState<Expert[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadExperts = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      const result = await api.listExperts();\r\n      \r\n      if (result.success) {\r\n        setExperts(result.experts);\r\n      } else {\r\n        setError(result.error || 'Failed to load experts');\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to load experts');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadExperts();\r\n  }, [refreshTrigger]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n        <span className=\"ml-2\">Loading experts...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"p-4 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n        <p>Error: {error}</p>\r\n        <button \r\n          onClick={loadExperts}\r\n          className=\"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\r\n        >\r\n          Retry\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (experts.length === 0) {\r\n    return (\r\n      <div className=\"text-center p-8 text-gray-500\">\r\n        <p>No experts created yet.</p>\r\n        <p className=\"text-sm mt-2\">Create your first AI expert to get started!</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <h3 className=\"text-lg font-semibold mb-4\">Your AI Experts ({experts.length})</h3>\r\n      \r\n      <div className=\"grid gap-4\">\r\n        {experts.map((expert) => (\r\n          <div\r\n            key={expert.id}\r\n            className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\"\r\n          >\r\n            <div className=\"flex justify-between items-start mb-2\">\r\n              <h4 className=\"font-semibold text-lg\">{expert.name}</h4>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\r\n                  {expert.model}\r\n                </span>\r\n                <button\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    onExpertEdit?.(expert);\r\n                  }}\r\n                  className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded hover:bg-green-200 transition-colors\"\r\n                >\r\n                  Edit\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {expert.description && (\r\n              <p className=\"text-gray-600 text-sm mb-2\">{expert.description}</p>\r\n            )}\r\n\r\n            {/* Labels */}\r\n            {expert.labels && expert.labels.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mb-2\">\r\n                {expert.labels.map((label, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\"\r\n                  >\r\n                    {label}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            <div className=\"text-xs text-gray-500 space-y-1\">\r\n              <p>Assistant ID: {expert.assistantId}</p>\r\n              <p>Pricing: {expert.pricingPercentage}% of token usage</p>\r\n              <p>Public: {expert.isPublic ? 'Yes' : 'No'}</p>\r\n              <p>Created: {formatDate(expert.createdAt)}</p>\r\n              {expert.updatedAt !== expert.createdAt && (\r\n                <p>Updated: {formatDate(expert.updatedAt)}</p>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n              <p className=\"text-xs text-gray-600\">\r\n                <strong>System Prompt:</strong> {expert.systemPrompt.substring(0, 100)}\r\n                {expert.systemPrompt.length > 100 && '...'}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Image */}\r\n            {expert.imageUrl && (\r\n              <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n                <img \r\n                  src={`http://localhost:3001${expert.imageUrl}`} \r\n                  alt={expert.name} \r\n                  className=\"w-16 h-16 object-cover rounded border\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Chat Button */}\r\n            <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n              <button\r\n                onClick={() => onExpertSelect?.(expert)}\r\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n              >\r\n                Chat with Expert\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpertList;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA0BA,MAAM,aAAwC;QAAC,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE;;IAC7F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc;QAClB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,WAAW;YAEpC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,OAAO;YAC3B,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;KAAe;IAEnB,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;IAG7B;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;wBAAE;wBAAQ;;;;;;;8BACX,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;8BAAE;;;;;;8BACH,6LAAC;oBAAE,WAAU;8BAAe;;;;;;;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;oBAA6B;oBAAkB,QAAQ,MAAM;oBAAC;;;;;;;0BAE5E,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB,OAAO,IAAI;;;;;;kDAClD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,OAAO,KAAK;;;;;;0DAEf,6LAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,yBAAA,mCAAA,aAAe;gDACjB;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAMJ,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CAA8B,OAAO,WAAW;;;;;;4BAI9D,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,mBACvC,6LAAC;gCAAI,WAAU;0CACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;0CASb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;4CAAe,OAAO,WAAW;;;;;;;kDACpC,6LAAC;;4CAAE;4CAAU,OAAO,iBAAiB;4CAAC;;;;;;;kDACtC,6LAAC;;4CAAE;4CAAS,OAAO,QAAQ,GAAG,QAAQ;;;;;;;kDACtC,6LAAC;;4CAAE;4CAAU,WAAW,OAAO,SAAS;;;;;;;oCACvC,OAAO,SAAS,KAAK,OAAO,SAAS,kBACpC,6LAAC;;4CAAE;4CAAU,WAAW,OAAO,SAAS;;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;sDAAO;;;;;;wCAAuB;wCAAE,OAAO,YAAY,CAAC,SAAS,CAAC,GAAG;wCACjE,OAAO,YAAY,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;4BAKxC,OAAO,QAAQ,kBACd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;oCAC5C,KAAK,OAAO,IAAI;oCAChB,WAAU;;;;;;;;;;;0CAMhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,2BAAA,qCAAA,eAAiB;oCAChC,WAAU;8CACX;;;;;;;;;;;;uBAxEE,OAAO,EAAE;;;;;;;;;;;;;;;;AAiF1B;GA7JM;KAAA;uCA+JS", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ExpertPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport C<PERSON><PERSON>x<PERSON> from './CreateExpert';\r\nimport <PERSON>Ex<PERSON> from './EditExpert';\r\nimport ExpertList from './ExpertList';\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nconst ExpertPanel: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState<'list' | 'create' | 'edit'>('list');\r\n  const [selectedExpert, setSelectedExpert] = useState<Expert | null>(null);\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\r\n\r\n  const handleExpertCreated = (expert: any) => {\r\n    console.log('Expert created:', expert);\r\n    setRefreshTrigger(prev => prev + 1);\r\n    setActiveTab('list');\r\n  };\r\n\r\n  const handleExpertUpdated = (expert: any) => {\r\n    console.log('Expert updated:', expert);\r\n    setRefreshTrigger(prev => prev + 1);\r\n    setActiveTab('list');\r\n    setSelectedExpert(null);\r\n  };\r\n\r\n  const handleExpertSelect = (expert: Expert) => {\r\n    console.log('Expert selected:', expert);\r\n    // You can add navigation or modal logic here\r\n  };\r\n\r\n  const handleExpertEdit = (expert: Expert) => {\r\n    console.log('Expert edit:', expert);\r\n    setSelectedExpert(expert);\r\n    setActiveTab('edit');\r\n  };\r\n\r\n  const handleCancelEdit = () => {\r\n    setSelectedExpert(null);\r\n    setActiveTab('list');\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto p-6\">\r\n      {/* Tab Navigation */}\r\n      <div className=\"flex border-b border-gray-200 mb-6\">\r\n        <button\r\n          onClick={() => setActiveTab('list')}\r\n          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\r\n            activeTab === 'list'\r\n              ? 'border-blue-600 text-blue-600'\r\n              : 'border-transparent text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          My Experts\r\n        </button>\r\n        <button\r\n          onClick={() => setActiveTab('create')}\r\n          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\r\n            activeTab === 'create'\r\n              ? 'border-blue-600 text-blue-600'\r\n              : 'border-transparent text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          Create Expert\r\n        </button>\r\n        {activeTab === 'edit' && selectedExpert && (\r\n          <button\r\n            className=\"px-6 py-3 font-medium text-sm border-b-2 border-orange-600 text-orange-600\"\r\n          >\r\n            Edit: {selectedExpert.name}\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"min-h-[400px]\">\r\n        {activeTab === 'list' && (\r\n          <ExpertList \r\n            onExpertSelect={handleExpertSelect}\r\n            onExpertEdit={handleExpertEdit}\r\n            refreshTrigger={refreshTrigger}\r\n          />\r\n        )}\r\n        \r\n        {activeTab === 'create' && (\r\n          <CreateExpert \r\n            onExpertCreated={handleExpertCreated}\r\n            onCancel={() => setActiveTab('list')}\r\n          />\r\n        )}\r\n\r\n        {activeTab === 'edit' && selectedExpert && (\r\n          <EditExpert \r\n            expert={selectedExpert}\r\n            onExpertUpdated={handleExpertUpdated}\r\n            onCancel={handleCancelEdit}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpertPanel;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAsBA,MAAM,cAAwB;;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,kBAAkB,CAAA,OAAQ,OAAO;QACjC,aAAa;IACf;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,kBAAkB,CAAA,OAAQ,OAAO;QACjC,aAAa;QACb,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,oBAAoB;IAChC,6CAA6C;IAC/C;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,AAAC,8DAIX,OAHC,cAAc,SACV,kCACA;kCAEP;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,AAAC,8DAIX,OAHC,cAAc,WACV,kCACA;kCAEP;;;;;;oBAGA,cAAc,UAAU,gCACvB,6LAAC;wBACC,WAAU;;4BACX;4BACQ,eAAe,IAAI;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,wBACb,6LAAC,mIAAA,CAAA,UAAU;wBACT,gBAAgB;wBAChB,cAAc;wBACd,gBAAgB;;;;;;oBAInB,cAAc,0BACb,6LAAC,qIAAA,CAAA,UAAY;wBACX,iBAAiB;wBACjB,UAAU,IAAM,aAAa;;;;;;oBAIhC,cAAc,UAAU,gCACvB,6LAAC,mIAAA,CAAA,UAAU;wBACT,QAAQ;wBACR,iBAAiB;wBACjB,UAAU;;;;;;;;;;;;;;;;;;AAMtB;GA9FM;KAAA;uCAgGS", "debugId": null}}]}