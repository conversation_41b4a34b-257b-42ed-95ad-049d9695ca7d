const mysql = require('mysql2/promise');

async function cleanupLabels() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'aitrainerhub'
    });

    // Get all experts with labels
    const [rows] = await connection.execute('SELECT id, labels FROM experts WHERE labels IS NOT NULL');

    console.log(`Found ${rows.length} experts with labels to check...`);

    for (const row of rows) {
      try {
        // Try to parse as JSON
        JSON.parse(row.labels);
        console.log(`✅ Expert ${row.id}: Valid JSON`);
      } catch (error) {
        console.log(`🔧 Expert ${row.id}: Invalid JSON, fixing...`);
        console.log(`   Original: ${row.labels}`);
        
        // Convert comma-separated string to JSON array
        let fixedLabels = [];
        if (typeof row.labels === 'string') {
          fixedLabels = row.labels.split(',').map(label => label.trim()).filter(label => label);
        }
        
        const jsonLabels = JSON.stringify(fixedLabels);
        console.log(`   Fixed: ${jsonLabels}`);
        
        // Update the database
        await connection.execute(
          'UPDATE experts SET labels = ? WHERE id = ?',
          [jsonLabels, row.id]
        );
        
        console.log(`✅ Expert ${row.id}: Updated successfully`);
      }
    }

    await connection.end();
    console.log('🎉 Cleanup completed successfully!');
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  }
}

cleanupLabels();