'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const Navigation = () => {
  const pathname = usePathname();

  const navItems = [
    { href: '/', label: 'Home' },
    { href: '/experts', label: 'AI Experts' },
    { href: '/chat', label: 'Chat' },
    { href: '/history', label: 'History' }
  ];

  return (
    <nav className="bg-white shadow-lg border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-2xl font-bold" style={{ color: '#1E3A8A' }}>
              AI Trainer Hub
            </Link>
            
            <div className="flex space-x-6">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    pathname === item.href
                      ? 'text-white shadow-lg'
                      : 'text-gray-600 hover:text-white hover:shadow-md'
                  }`}
                  style={pathname === item.href 
                    ? { backgroundColor: '#1E3A8A' }
                    : { backgroundColor: 'transparent' }
                  }
                  onMouseEnter={(e) => {
                    if (pathname !== item.href) {
                      e.currentTarget.style.backgroundColor = '#1E3A8A';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (pathname !== item.href) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          
          <div className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
            🔑 Connected
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;