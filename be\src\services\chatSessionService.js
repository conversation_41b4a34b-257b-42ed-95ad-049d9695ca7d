const db = require('../config/database');

class ChatSessionService {
    // Create or get existing chat session
    static async createOrGetSession(userId, expertId = null, threadId = null) {
        try {
            // If threadId is provided, try to get existing session
            if (threadId) {
                const [existingSessions] = await db.execute(
                    'SELECT * FROM chat_sessions WHERE thread_id = ? AND user_id = ?',
                    [threadId, userId]
                );
                
                if (existingSessions.length > 0) {
                    return {
                        success: true,
                        session: existingSessions[0],
                        isNew: false
                    };
                }
            }

            // Get expert info if expertId provided
            let expertName = null;
            let expertModel = null;
            let assistantId = null;
            if (expertId) {
                const [experts] = await db.execute(
                    'SELECT name, model, assistant_id FROM experts WHERE id = ?',
                    [expertId]
                );
                if (experts.length > 0) {
                    expertName = experts[0].name;
                    expertModel = experts[0].model;
                    assistantId = experts[0].assistant_id;
                }
            }

            // We'll let the chatService create the OpenAI thread ID
            // For now, we'll use a placeholder that will be updated after first message
            const tempThreadId = threadId || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Create new session
            const [result] = await db.execute(
                `INSERT INTO chat_sessions (user_id, thread_id, expert_id, expert_name, expert_model, session_title) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [
                    userId, 
                    tempThreadId, 
                    expertId, 
                    expertName, 
                    expertModel,
                    expertName ? `Chat with ${expertName}` : 'General Chat'
                ]
            );

            // Update user stats
            await this.updateUserStats(userId);

            const [newSession] = await db.execute(
                'SELECT * FROM chat_sessions WHERE id = ?',
                [result.insertId]
            );

            return {
                success: true,
                session: newSession[0],
                isNew: true,
                expertContext: assistantId ? {
                    assistantId,
                    name: expertName,
                    model: expertModel
                } : null
            };

        } catch (error) {
            console.error('Error creating chat session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Save chat message
    static async saveMessage(sessionId, threadId, role, content, tokensUsed = 0, cost = 0) {
        try {
            // Get message order
            const [orderResult] = await db.execute(
                'SELECT COALESCE(MAX(message_order), 0) + 1 as next_order FROM chat_messages WHERE session_id = ?',
                [sessionId]
            );
            const messageOrder = orderResult[0].next_order;

            // Insert message
            const [result] = await db.execute(
                `INSERT INTO chat_messages (session_id, thread_id, role, content, message_order, tokens_used, cost) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [sessionId, threadId, role, content, messageOrder, tokensUsed, cost]
            );

            // Update session timestamp
            await db.execute(
                'UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sessionId]
            );

            // Update user stats
            const [session] = await db.execute(
                'SELECT user_id FROM chat_sessions WHERE id = ?',
                [sessionId]
            );
            
            if (session.length > 0) {
                await this.updateUserStats(session[0].user_id);
            }

            return {
                success: true,
                messageId: result.insertId
            };

        } catch (error) {
            console.error('Error saving message:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }


   

    // Get chat history by session ID (more efficient)
    static async getChatHistoryBySessionId(sessionId, userId, limit = 50) {
        try {
            console.log('getChatHistoryBySessionId called with:', { sessionId, userId, limit });
            
            // Ensure limit is a valid number
            const validLimit = Math.max(1, Math.min(parseInt(limit) || 50, 100));
            
            // Verify user owns this session
            const [sessions] = await db.execute(
                'SELECT id FROM chat_sessions WHERE id = ? AND user_id = ?',
                [sessionId, userId]
            );
            
            if (sessions.length === 0) {
                return {
                    success: false,
                    error: 'Session not found or access denied'
                };
            }
            
            // Get messages for this session
            const [messages] = await db.execute(
                `SELECT cm.*, cs.expert_name 
                 FROM chat_messages cm
                 JOIN chat_sessions cs ON cm.session_id = cs.id
                 WHERE cm.session_id = ?
                 ORDER BY cm.message_order ASC
                 LIMIT ?`,
                [sessionId, validLimit]
            );

            console.log('Found messages:', messages.length);

            return {
                success: true,
                messages: messages
            };

        } catch (error) {
            console.error('Error getting chat history by session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get user's chat sessions
    static async getUserSessions(userId, limit = 20) {
        try {
            console.log('ChatSessionService.getUserSessions called with:', { userId, limit });
            
            // Ensure limit is a valid number
            const validLimit = Math.max(1, Math.min(parseInt(limit) || 20, 100));
            
            // First, try simple query without complex joins
            const [sessions] = await db.execute(
                `SELECT 
                    id,
                    user_id,
                    thread_id,
                    expert_id,
                    expert_name,
                    expert_model,
                    session_title,
                    created_at,
                    updated_at,
                    is_active
                 FROM chat_sessions 
                 WHERE user_id = ? AND is_active = TRUE
                 ORDER BY updated_at DESC
                 LIMIT ?`,
                [userId, validLimit]
            );

            console.log('Found basic sessions:', sessions.length);

            // Then get message counts for each session
            const enrichedSessions = [];
            for (const session of sessions) {
                try {
                    const [messageStats] = await db.execute(
                        `SELECT 
                            COUNT(*) as message_count,
                            MAX(created_at) as last_message_at,
                            SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_messages,
                            SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as assistant_messages,
                            COALESCE(SUM(tokens_used), 0) as total_tokens,
                            COALESCE(SUM(cost), 0) as total_cost
                         FROM chat_messages 
                         WHERE session_id = ?`,
                        [session.id]
                    );

                    enrichedSessions.push({
                        ...session,
                        message_count: messageStats[0].message_count || 0,
                        last_message_at: messageStats[0].last_message_at,
                        user_messages: messageStats[0].user_messages || 0,
                        assistant_messages: messageStats[0].assistant_messages || 0,
                        total_tokens: messageStats[0].total_tokens || 0,
                        total_cost: parseFloat(messageStats[0].total_cost || 0)
                    });
                } catch (msgError) {
                    console.error('Error getting message stats for session', session.id, ':', msgError);
                    // Include session even if message stats fail
                    enrichedSessions.push({
                        ...session,
                        message_count: 0,
                        last_message_at: null,
                        user_messages: 0,
                        assistant_messages: 0,
                        total_tokens: 0,
                        total_cost: 0
                    });
                }
            }

            console.log('Enriched sessions:', enrichedSessions.length);

            return {
                success: true,
                sessions: enrichedSessions
            };

        } catch (error) {
            console.error('Error getting user sessions:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update session title
    static async updateSessionTitle(sessionId, userId, title) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET session_title = ? WHERE id = ? AND user_id = ?',
                [title, sessionId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating session title:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Delete chat session
    static async deleteSession(sessionId, userId) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET is_active = FALSE WHERE id = ? AND user_id = ?',
                [sessionId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error deleting session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update user statistics
    static async updateUserStats(userId) {
        try {
            await db.execute(
                `INSERT INTO user_chat_stats (
                    user_id, 
                    total_sessions, 
                    total_messages, 
                    total_tokens_used, 
                    total_cost, 
                    last_chat_at
                ) 
                SELECT 
                    ? as user_id,
                    COUNT(DISTINCT cs.id) as total_sessions,
                    COUNT(cm.id) as total_messages,
                    COALESCE(SUM(cm.tokens_used), 0) as total_tokens_used,
                    COALESCE(SUM(cm.cost), 0) as total_cost,
                    MAX(cm.created_at) as last_chat_at
                FROM chat_sessions cs
                LEFT JOIN chat_messages cm ON cs.id = cm.session_id
                WHERE cs.user_id = ? AND cs.is_active = TRUE
                ON DUPLICATE KEY UPDATE
                    total_sessions = VALUES(total_sessions),
                    total_messages = VALUES(total_messages),
                    total_tokens_used = VALUES(total_tokens_used),
                    total_cost = VALUES(total_cost),
                    last_chat_at = VALUES(last_chat_at),
                    updated_at = CURRENT_TIMESTAMP`,
                [userId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating user stats:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update thread ID after OpenAI thread creation
    static async updateThreadId(sessionId, newThreadId) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET thread_id = ? WHERE id = ?',
                [newThreadId, sessionId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating thread ID:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get session by thread ID
    static async getSessionByThreadId(threadId, userId) {
        try {
            const [sessions] = await db.execute(
                'SELECT * FROM chat_sessions WHERE thread_id = ? AND user_id = ?',
                [threadId, userId]
            );

            return {
                success: true,
                session: sessions.length > 0 ? sessions[0] : null
            };

        } catch (error) {
            console.error('Error getting session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get most recent active session for a specific expert and user
    static async getActiveSessionForExpert(userId, expertId) {
        try {
            const [sessions] = await db.execute(
                `SELECT * FROM chat_sessions 
                 WHERE user_id = ? AND expert_id = ? AND is_active = TRUE
                 ORDER BY updated_at DESC 
                 LIMIT 1`,
                [userId, expertId]
            );

            return {
                success: true,
                session: sessions.length > 0 ? sessions[0] : null
            };

        } catch (error) {
            console.error('Error getting active session for expert:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = ChatSessionService;
