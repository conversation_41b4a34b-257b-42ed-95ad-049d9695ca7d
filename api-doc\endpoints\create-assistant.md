# Create Assistant

Endpoint untuk membuat assistant baru dengan optional file upload.

## Endpoint
```
POST /api/assistants
```

## Request

### Headers
```
Content-Type: multipart/form-data
```

### Form Data Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | Yes | Nama assistant |
| instructions | string | Yes | Instruksi untuk assistant |
| model | string | No | Model OpenAI yang digunakan (default: gpt-4o-mini) |
| file | file | No | File untuk knowledge base assistant |
| userId | string | Yes | ID user untuk kepemilikan assistant |

### Supported Models
- gpt-3.5-turbo
- gpt-4
- gpt-4-turbo
- gpt-4-turbo-preview
- gpt-4o
- gpt-4o-mini

### Supported File Types
- PDF (.pdf)
- Text (.txt)
- Word Document (.docx, .doc)
- Markdown (.md)
- JSON (.json)

## Response

### Success (200)
```json
{
  "success": true,
  "assistant": {
    "id": "asst_abc123",
    "name": "Customer Support Bot",
    "instructions": "You are a helpful customer support assistant...",
    "model": "gpt-4o-mini",
    "tools": [
      {
        "type": "file_search"
      }
    ],
    "tool_resources": {
      "file_search": {
        "vector_store_ids": ["vs_abc123"]
      }
    }
  }
}
```

### Error (400) - Missing User ID
```json
{
  "success": false,
  "error": "User ID is required"
}
```

### Error (400) - Validation Error
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    "Name is required",
    "Instructions are required"
  ]
}
```

### Error (400) - Invalid File Type
```json
{
  "success": false,
  "error": "Invalid file type",
  "details": [
    "Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"
  ]
}
```

### Error (500) - Server Error
```json
{
  "success": false,
  "error": "Internal server error",
  "message": "Detailed error message"
}
```

## Response Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Status keberhasilan operasi |
| assistant | object | Data assistant yang dibuat |
| assistant.id | string | ID unik assistant |
| assistant.name | string | Nama assistant |
| assistant.instructions | string | Instruksi assistant |
| assistant.model | string | Model yang digunakan |
| assistant.tools | array | Tools yang tersedia untuk assistant |
| assistant.tool_resources | object | Resource tools (vector store untuk file search) |

## Contoh Penggunaan

### cURL (tanpa file)
```bash
curl -X POST http://localhost:3001/api/assistants \
  -F "name=Customer Support Bot" \
  -F "instructions=You are a helpful customer support assistant" \
  -F "model=gpt-4o-mini" \
  -F "userId=user_123"
```

### cURL (dengan file)
```bash
curl -X POST http://localhost:3001/api/assistants \
  -F "name=Document Assistant" \
  -F "instructions=You are an assistant that helps with document analysis" \
  -F "model=gpt-4o-mini" \
  -F "userId=user_123" \
  -F "file=@/path/to/document.pdf"
```

### JavaScript (FormData)
```javascript
const formData = new FormData();
formData.append('name', 'Customer Support Bot');
formData.append('instructions', 'You are a helpful customer support assistant');
formData.append('model', 'gpt-4o-mini');

// Optional: tambahkan file
const fileInput = document.getElementById('fileInput');
if (fileInput.files[0]) {
  formData.append('file', fileInput.files[0]);
}

const response = await fetch('http://localhost:3001/api/assistants', {
  method: 'POST',
  body: formData
});

const data = await response.json();
console.log(data);
```

## Catatan
- File maksimal 20MB
- Jika file disediakan, assistant akan dilengkapi dengan file_search tool
- File akan diproses ke dalam vector store untuk pencarian
- Temporary file akan dihapus setelah diproses