{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/client-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { useState } from \"react\";\n\nexport default function ClientProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => new QueryClient());\n  \n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKe,SAAS,eAAe,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACrC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAAE,IAAM,IAAI,gLAAA,CAAA,cAAW;;IAEpD,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP;GARwB;KAAA", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\n\r\nconst Navigation = () => {\r\n  const pathname = usePathname();\r\n\r\n  const navItems = [\r\n    { href: '/', label: 'Home' },\r\n    { href: '/experts', label: 'AI Experts' },\r\n    { href: '/chat', label: 'Chat' },\r\n    { href: '/history', label: 'History' }\r\n  ];\r\n\r\n  return (\r\n    <nav className=\"bg-white shadow-lg border-b border-gray-100\">\r\n      <div className=\"max-w-7xl mx-auto px-4\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          <div className=\"flex items-center space-x-8\">\r\n            <Link href=\"/\" className=\"text-2xl font-bold\" style={{ color: '#1E3A8A' }}>\r\n              AI Trainer Hub\r\n            </Link>\r\n            \r\n            <div className=\"flex space-x-6\">\r\n              {navItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\r\n                    pathname === item.href\r\n                      ? 'text-white shadow-lg'\r\n                      : 'text-gray-600 hover:text-white hover:shadow-md'\r\n                  }`}\r\n                  style={pathname === item.href \r\n                    ? { backgroundColor: '#1E3A8A' }\r\n                    : { backgroundColor: 'transparent' }\r\n                  }\r\n                  onMouseEnter={(e) => {\r\n                    if (pathname !== item.href) {\r\n                      e.currentTarget.style.backgroundColor = '#1E3A8A';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (pathname !== item.href) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                >\r\n                  {item.label}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full\">\r\n            🔑 Connected\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navigation;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAY,OAAO;QAAa;QACxC;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;gCAAqB,OAAO;oCAAE,OAAO;gCAAU;0CAAG;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,AAAC,wEAIX,OAHC,aAAa,KAAK,IAAI,GAClB,yBACA;wCAEN,OAAO,aAAa,KAAK,IAAI,GACzB;4CAAE,iBAAiB;wCAAU,IAC7B;4CAAE,iBAAiB;wCAAc;wCAErC,cAAc,CAAC;4CACb,IAAI,aAAa,KAAK,IAAI,EAAE;gDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;wCACF;wCACA,cAAc,CAAC;4CACb,IAAI,aAAa,KAAK,IAAI,EAAE;gDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;wCACF;kDAEC,KAAK,KAAK;uCAtBN,KAAK,IAAI;;;;;;;;;;;;;;;;kCA4BtB,6LAAC;wBAAI,WAAU;kCAA0D;;;;;;;;;;;;;;;;;;;;;;AAOnF;GAzDM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCA2DS", "debugId": null}}]}