{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/api.ts"], "sourcesContent": ["const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\ninterface ApiOptions {\r\n    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\r\n    body?: any;\r\n    headers?: Record<string, string>;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n    const { method = 'GET', body, headers = {} } = options;\r\n\r\n    const fullUrl = `${API_URL}${endpoint}`;\r\n\r\n    // Debug logging\r\n    console.log('🔍 API Call Debug:', {\r\n        endpoint,\r\n        fullUrl,\r\n        API_URL,\r\n        TOKEN: TOKEN.substring(0, 3) + '***',\r\n        method,\r\n        body,\r\n        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,\r\n        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN\r\n    });\r\n\r\n    const config: RequestInit = {\r\n        method,\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${TOKEN}`,\r\n            ...headers,\r\n        },\r\n    };\r\n\r\n    if (body && method !== 'GET') {\r\n        config.body = JSON.stringify(body);\r\n    }\r\n\r\n    try {\r\n        const response = await fetch(fullUrl, config);\r\n\r\n        console.log('📡 Response status:', response.status, response.statusText);\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            console.error('❌ API Error:', errorData);\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('✅ API Success:', data);\r\n        return data;\r\n    } catch (error) {\r\n        console.error('💥 API call failed:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n    // Health check (no token required)\r\n    health: () => apiCall('/health'),\r\n\r\n    // Chat endpoints (token required)\r\n    chat: (message: string, threadId?: string) => apiCall('/api/chat', {\r\n        method: 'POST',\r\n        body: { message, threadId }\r\n    }),\r\n\r\n    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n    // Assistant endpoints (token required)\r\n    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),\r\n\r\n    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {\r\n        method: 'POST',\r\n        body: { threadId, message }\r\n    }),\r\n\r\n    runAssistant: (threadId: string) => apiCall('/assistant/run', {\r\n        method: 'POST',\r\n        body: { threadId }\r\n    }),\r\n\r\n    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n    // Expert endpoints (token required)\r\n    createExpert: (expertData: FormData) => {\r\n        return fetch(`${API_URL}/api/experts`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: expertData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n\r\n    listExperts: () => apiCall('/api/experts'),\r\n\r\n    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {\r\n        const formData = new FormData();\r\n        \r\n        // Add text fields\r\n        Object.keys(expertData).forEach(key => {\r\n            if (expertData[key] !== undefined && expertData[key] !== null) {\r\n                if (key === 'labels' && Array.isArray(expertData[key])) {\r\n                    formData.append(key, JSON.stringify(expertData[key]));\r\n                } else {\r\n                    formData.append(key, expertData[key].toString());\r\n                }\r\n            }\r\n        });\r\n\r\n        // Add files\r\n        if (knowledgeBaseFile) {\r\n            formData.append('file', knowledgeBaseFile);\r\n        }\r\n        if (imageFile) {\r\n            formData.append('image', imageFile);\r\n        }\r\n\r\n        return fetch(`${API_URL}/api/experts/${expertId}`, {\r\n            method: 'PUT',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: formData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n};"], "names": [], "mappings": ";;;;AAAgB;AAAhB,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAQxC,eAAe,QAAQ,QAAgB;QAAE,UAAA,iEAAsB,CAAC;IACnE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG;IAE/C,MAAM,UAAU,AAAC,GAAY,OAAV,SAAmB,OAAT;IAE7B,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAC9B;QACA;QACA;QACA,OAAO,MAAM,SAAS,CAAC,GAAG,KAAK;QAC/B;QACA;QACA,iCAAiC;QACjC,+BAA+B;IACnC;IAEA,MAAM,SAAsB;QACxB;QACA,SAAS;YACL,gBAAgB;YAChB,iBAAiB,AAAC,UAAe,OAAN;YAC3B,GAAG,OAAO;QACd;IACJ;IAEA,IAAI,QAAQ,WAAW,OAAO;QAC1B,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC;IACjC;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;QAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACV;AACJ;AAGO,MAAM,MAAM;IACf,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CAAC,SAAiB,WAAsB,QAAQ,aAAa;YAC/D,QAAQ;YACR,MAAM;gBAAE;gBAAS;YAAS;QAC9B;IAEA,mBAAmB,CAAC,WAAqB,QAAQ,AAAC,eAAuB,OAAT,UAAS;IAEzE,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAAoB,QAAQ,sBAAsB;YAC9E,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC9B;IAEA,cAAc,CAAC,WAAqB,QAAQ,kBAAkB;YAC1D,QAAQ;YACR,MAAM;gBAAE;YAAS;QACrB;IAEA,aAAa,CAAC,WAAqB,QAAQ,AAAC,uBAA+B,OAAT;IAElE,oCAAoC;IACpC,cAAc,CAAC;QACX,OAAO,MAAM,AAAC,GAAU,OAAR,SAAQ,iBAAe;YACnC,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;IAEA,aAAa,IAAM,QAAQ;IAE3B,WAAW,CAAC,WAAqB,QAAQ,AAAC,gBAAwB,OAAT;IAEzD,cAAc,CAAC,UAAkB,YAAiB,mBAAiC;QAC/E,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC3D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACpD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACvD,OAAO;oBACH,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBACjD;YACJ;QACJ;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACnB,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,IAAI,WAAW;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,OAAO,MAAM,AAAC,GAAyB,OAAvB,SAAQ,iBAAwB,OAAT,WAAY;YAC/C,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ApiExample.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { api } from '@/lib/api';\r\n\r\nexport default function ApiExample() {\r\n  const [message, setMessage] = useState('');\r\n  const [response, setResponse] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  \r\n  // Get configuration values\r\n  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n  const token = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!message.trim()) return;\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setResponse('');\r\n\r\n    try {\r\n      // Semua panggilan API otomatis menggunakan token dari environment\r\n      const result = await api.chat(message);\r\n      setResponse(JSON.stringify(result, null, 2));\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'An error occurred');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testHealthCheck = async () => {\r\n    setLoading(true);\r\n    setError('');\r\n    setResponse('');\r\n\r\n    try {\r\n      const result = await api.health();\r\n      setResponse(JSON.stringify(result, null, 2));\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'An error occurred');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testAssistant = async () => {\r\n    setLoading(true);\r\n    setError('');\r\n    setResponse('');\r\n\r\n    try {\r\n      // Create thread\r\n      const thread = await api.createThread();\r\n      setResponse(`Thread created: ${JSON.stringify(thread, null, 2)}`);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'An error occurred');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6 max-w-4xl mx-auto\">\r\n      <h2 className=\"text-2xl font-bold mb-4\">API Test dengan Token Authentication</h2>\r\n      \r\n      {/* Configuration Display */}\r\n      <div className=\"mb-6 p-4 bg-blue-50 border border-blue-200 rounded\">\r\n        <h3 className=\"font-semibold text-blue-800 mb-2\">Current Configuration:</h3>\r\n        <div className=\"text-blue-700 text-sm space-y-1\">\r\n          <div><strong>Backend URL:</strong> {apiUrl}</div>\r\n          <div><strong>Token:</strong> {token.substring(0, 3)}***</div>\r\n          <div><strong>Full Chat URL:</strong> {apiUrl}/api/chat</div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"space-y-4\">\r\n        {/* Health Check Test */}\r\n        <div>\r\n          <button\r\n            onClick={testHealthCheck}\r\n            disabled={loading}\r\n            className=\"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50\"\r\n          >\r\n            Test Health Check ({apiUrl}/health)\r\n          </button>\r\n        </div>\r\n\r\n        {/* Chat Test */}\r\n        <div>\r\n          <div className=\"flex gap-2 mb-2\">\r\n            <input\r\n              type=\"text\"\r\n              value={message}\r\n              onChange={(e) => setMessage(e.target.value)}\r\n              placeholder=\"Enter your message...\"\r\n              className=\"flex-1 border border-gray-300 rounded px-3 py-2\"\r\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\r\n            />\r\n            <button\r\n              onClick={handleSendMessage}\r\n              disabled={loading || !message.trim()}\r\n              className=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50\"\r\n            >\r\n              Send Chat ({apiUrl}/api/chat)\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Assistant Test */}\r\n        <div>\r\n          <button\r\n            onClick={testAssistant}\r\n            disabled={loading}\r\n            className=\"bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50\"\r\n          >\r\n            Test Assistant ({apiUrl}/assistant/thread)\r\n          </button>\r\n        </div>\r\n\r\n        {/* Loading State */}\r\n        {loading && (\r\n          <div className=\"text-blue-500 font-medium\">Loading...</div>\r\n        )}\r\n\r\n        {/* Error Display */}\r\n        {error && (\r\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\r\n            <strong>Error:</strong> {error}\r\n          </div>\r\n        )}\r\n\r\n        {/* Response Display */}\r\n        {response && (\r\n          <div className=\"bg-gray-100 border border-gray-300 rounded p-4\">\r\n            <h3 className=\"font-semibold mb-2\">Response:</h3>\r\n            <pre className=\"whitespace-pre-wrap text-sm overflow-x-auto\">{response}</pre>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <h3 className=\"font-semibold text-yellow-800\">Troubleshooting:</h3>\r\n        <ul className=\"text-yellow-700 text-sm mt-2 space-y-1\">\r\n          <li>• Pastikan backend berjalan di: <code className=\"bg-yellow-100 px-1 rounded\">cd be && npm run dev</code></li>\r\n          <li>• Pastikan database MySQL berjalan dengan database 'aitrainerhub'</li>\r\n          <li>• Check browser console untuk debug logs</li>\r\n          <li>• Pastikan tidak ada CORS issues</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;AAYiB;;AAVjB;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,2BAA2B;IAC3B,MAAM,SAAS,6DAAmC;IAClD,MAAM,QAAQ,6CAAiC;IAE/C,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,WAAW;QACX,SAAS;QACT,YAAY;QAEZ,IAAI;YACF,kEAAkE;YAClE,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,IAAI,CAAC;YAC9B,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,YAAY;QAEZ,IAAI;YACF,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,MAAM;YAC/B,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,SAAS;QACT,YAAY;QAEZ,IAAI;YACF,gBAAgB;YAChB,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,YAAY;YACrC,YAAY,AAAC,mBAAkD,OAAhC,KAAK,SAAS,CAAC,QAAQ,MAAM;QAC9D,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAGxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAqB;oCAAE;;;;;;;0CACpC,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAe;oCAAE,MAAM,SAAS,CAAC,GAAG;oCAAG;;;;;;;0CACpD,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAuB;oCAAE;oCAAO;;;;;;;;;;;;;;;;;;;0BAIjD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;kCACC,cAAA,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;gCACX;gCACqB;gCAAO;;;;;;;;;;;;kCAK/B,6LAAC;kCACC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,aAAY;oCACZ,WAAU;oCACV,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;8CAEzC,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW,CAAC,QAAQ,IAAI;oCAClC,WAAU;;wCACX;wCACa;wCAAO;;;;;;;;;;;;;;;;;;kCAMzB,6LAAC;kCACC,cAAA,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;gCACX;gCACkB;gCAAO;;;;;;;;;;;;oBAK3B,yBACC,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;oBAI5C,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAO;;;;;;4BAAe;4BAAE;;;;;;;oBAK5B,0BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;0BAKpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;;oCAAG;kDAAgC,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CACjF,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GArJwB;KAAA", "debugId": null}}]}