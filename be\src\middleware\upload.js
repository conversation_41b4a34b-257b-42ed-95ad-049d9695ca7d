const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  if (file.fieldname === 'file') {
    // Knowledge base file validation
    const allowedMimeTypes = [
      'application/pdf',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/markdown',
      'application/json'
    ];

    const allowedExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedMimeTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON'), false);
    }
  } else if (file.fieldname === 'image') {
    // Image file validation
    const allowedImageMimeTypes = [
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'image/webp'
    ];

    const allowedImageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedImageMimeTypes.includes(file.mimetype) || allowedImageExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP'), false);
    }
  } else {
    cb(new Error('Invalid field name for file upload'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 2 // Allow up to 2 files (knowledge base + image)
  }
});

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large',
        details: ['File size must be less than 10MB']
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files',
        details: ['Only one file is allowed']
      });
    }
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid file type',
      details: [error.message]
    });
  }

  next(error);
};

module.exports = {
  upload: upload.fields([
    { name: 'file', maxCount: 1 },    // Knowledge base file
    { name: 'image', maxCount: 1 }    // Image file
  ]),
  handleUploadError
};