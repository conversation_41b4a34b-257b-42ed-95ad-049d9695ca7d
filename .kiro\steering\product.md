# AI Trainer Hub

AI Trainer Hub is a chat application that enables users to interact with AI assistants through a clean web interface. The platform allows users to create and manage custom AI experts backed by OpenAI's Assistant API.

## Core Features

- **Chat Interface**: Real-time chat with AI assistants
- **Expert Management**: Create, configure, and manage custom AI experts
- **File Upload**: Support for knowledge base files (PDF, TXT, DOCX, MD, JSON)
- **User Authentication**: Token-based authentication system
- **Assistant API Integration**: Full integration with OpenAI's Assistant API

## Architecture

The application uses a separated frontend/backend architecture:
- **Backend**: Express.js server acting as a secure proxy to OpenAI API
- **Frontend**: Next.js application providing the user interface
- **Database**: MySQL for storing user data and expert configurations
- **Security**: API keys and sensitive data kept server-side only

## Target Users

Developers and users who want to create and interact with custom AI assistants with specific knowledge bases and configurations.