# Get Assistant

Endpoint untuk mendapatkan detail assistant berdasarkan ID.

## Endpoint
```
GET /api/assistants/:assistantId
```

## Request

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| assistantId | string | Yes | ID assistant yang ingin diambil detailnya |

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| userId | string | Yes | ID user untuk validasi kepemilikan assistant |

## Response

### Success (200)
```json
{
  "success": true,
  "assistant": {
    "id": "asst_abc123",
    "name": "Customer Support Bot",
    "instructions": "You are a helpful customer support assistant...",
    "model": "gpt-4o-mini",
    "created_at": 1703123456,
    "tools": [
      {
        "type": "file_search"
      }
    ],
    "file_ids": ["file_abc123"]
  }
}
```

### Error (400) - Missing User ID
```json
{
  "success": false,
  "error": "User ID is required"
}
```

### Error (500) - Server Error
```json
{
  "success": false,
  "error": "Failed to get assistant",
  "message": "Detailed error message"
}
```

## Response Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Status keberhasilan operasi |
| assistant | object | Data detail assistant |
| assistant.id | string | ID unik assistant |
| assistant.name | string | Nama assistant |
| assistant.instructions | string | Instruksi assistant |
| assistant.model | string | Model yang digunakan |
| assistant.created_at | number | Timestamp pembuatan (Unix timestamp) |
| assistant.tools | array | Tools yang tersedia untuk assistant |
| assistant.file_ids | array | Array ID file yang terkait dengan assistant |

## Contoh Penggunaan

### cURL
```bash
curl -X GET "http://localhost:3001/api/assistants/asst_abc123?userId=user_123"
```

### JavaScript (Fetch)
```javascript
const assistantId = 'asst_abc123';
const response = await fetch(`http://localhost:3001/api/assistants/${assistantId}`);
const data = await response.json();
console.log(data.assistant);
```

### Response Example
```json
{
  "success": true,
  "assistant": {
    "id": "asst_abc123",
    "name": "Customer Support Bot",
    "instructions": "You are a helpful customer support assistant that provides friendly and accurate responses to customer inquiries.",
    "model": "gpt-4o-mini",
    "created_at": 1703123456,
    "tools": [
      {
        "type": "file_search"
      }
    ],
    "file_ids": ["file_abc123"]
  }
}
```

## Catatan
- Assistant ID harus valid dan ada dalam sistem
- Jika assistant tidak ditemukan, akan mengembalikan error 500
- file_ids berisi ID file yang diupload saat membuat assistant