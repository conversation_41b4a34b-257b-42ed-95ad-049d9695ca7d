const express = require('express');
const expertController = require('../controllers/expertController');
const { upload, handleUploadError } = require('../middleware/upload');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Create expert endpoint with optional file upload
router.post('/api/experts', authenticateToken, upload, handleUploadError, expertController.createExpert.bind(expertController));

// List all experts for user
router.get('/api/experts', authenticateToken, expertController.listExperts.bind(expertController));

// Get specific expert
router.get('/api/experts/:expertId', authenticateToken, expertController.getExpert.bind(expertController));

// Update expert endpoint with optional file upload
router.put('/api/experts/:expertId', authenticateToken, upload, handleUploadError, expertController.updateExpert.bind(expertController));

module.exports = router;