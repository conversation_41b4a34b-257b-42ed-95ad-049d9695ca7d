{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/api.ts"], "sourcesContent": ["const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\ninterface ApiOptions {\r\n    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\r\n    body?: any;\r\n    headers?: Record<string, string>;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n    const { method = 'GET', body, headers = {} } = options;\r\n\r\n    const fullUrl = `${API_URL}${endpoint}`;\r\n\r\n    // Debug logging\r\n    console.log('🔍 API Call Debug:', {\r\n        endpoint,\r\n        fullUrl,\r\n        API_URL,\r\n        TOKEN: TOKEN.substring(0, 3) + '***',\r\n        method,\r\n        body,\r\n        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,\r\n        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN\r\n    });\r\n\r\n    const config: RequestInit = {\r\n        method,\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${TOKEN}`,\r\n            ...headers,\r\n        },\r\n    };\r\n\r\n    if (body && method !== 'GET') {\r\n        config.body = JSON.stringify(body);\r\n    }\r\n\r\n    try {\r\n        const response = await fetch(fullUrl, config);\r\n\r\n        console.log('📡 Response status:', response.status, response.statusText);\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            console.error('❌ API Error:', errorData);\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('✅ API Success:', data);\r\n        return data;\r\n    } catch (error) {\r\n        console.error('💥 API call failed:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n    // Health check (no token required)\r\n    health: () => apiCall('/health'),\r\n\r\n    // Chat endpoints (token required)\r\n    chat: (message: string, threadId?: string, expertId?: string, expertContext?: any) => apiCall('/api/chat', {\r\n        method: 'POST',\r\n        body: { message, threadId, expertId, expertContext }\r\n    }),\r\n\r\n    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n    // Chat session endpoints\r\n    getUserChatSessions: (limit?: number) => apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ''}`),\r\n\r\n    getActiveSessionForExpert: (expertId: string) => apiCall(`/api/chat/sessions/expert/${expertId}`),\r\n\r\n    updateSessionTitle: (sessionId: string, title: string) => apiCall(`/api/chat/sessions/${sessionId}/title`, {\r\n        method: 'PUT',\r\n        body: { title }\r\n    }),\r\n\r\n    deleteSession: (sessionId: string) => apiCall(`/api/chat/sessions/${sessionId}`, {\r\n        method: 'DELETE'\r\n    }),\r\n\r\n    // Assistant endpoints (token required)\r\n    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),\r\n\r\n    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {\r\n        method: 'POST',\r\n        body: { threadId, message }\r\n    }),\r\n\r\n    runAssistant: (threadId: string) => apiCall('/assistant/run', {\r\n        method: 'POST',\r\n        body: { threadId }\r\n    }),\r\n\r\n    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n    // Expert endpoints (token required)\r\n    createExpert: (expertData: FormData) => {\r\n        return fetch(`${API_URL}/api/experts`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: expertData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n\r\n    listExperts: () => apiCall('/api/experts'),\r\n\r\n    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {\r\n        const formData = new FormData();\r\n        \r\n        // Add text fields\r\n        Object.keys(expertData).forEach(key => {\r\n            if (expertData[key] !== undefined && expertData[key] !== null) {\r\n                if (key === 'labels' && Array.isArray(expertData[key])) {\r\n                    formData.append(key, JSON.stringify(expertData[key]));\r\n                } else {\r\n                    formData.append(key, expertData[key].toString());\r\n                }\r\n            }\r\n        });\r\n\r\n        // Add files\r\n        if (knowledgeBaseFile) {\r\n            formData.append('file', knowledgeBaseFile);\r\n        }\r\n        if (imageFile) {\r\n            formData.append('image', imageFile);\r\n        }\r\n\r\n        return fetch(`${API_URL}/api/experts/${expertId}`, {\r\n            method: 'PUT',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: formData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n};"], "names": [], "mappings": ";;;;AAAgB;AAAhB,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAQxC,eAAe,QAAQ,QAAgB;QAAE,UAAA,iEAAsB,CAAC;IACnE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG;IAE/C,MAAM,UAAU,AAAC,GAAY,OAAV,SAAmB,OAAT;IAE7B,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAC9B;QACA;QACA;QACA,OAAO,MAAM,SAAS,CAAC,GAAG,KAAK;QAC/B;QACA;QACA,iCAAiC;QACjC,+BAA+B;IACnC;IAEA,MAAM,SAAsB;QACxB;QACA,SAAS;YACL,gBAAgB;YAChB,iBAAiB,AAAC,UAAe,OAAN;YAC3B,GAAG,OAAO;QACd;IACJ;IAEA,IAAI,QAAQ,WAAW,OAAO;QAC1B,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC;IACjC;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;QAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACV;AACJ;AAGO,MAAM,MAAM;IACf,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CAAC,SAAiB,UAAmB,UAAmB,gBAAwB,QAAQ,aAAa;YACvG,QAAQ;YACR,MAAM;gBAAE;gBAAS;gBAAU;gBAAU;YAAc;QACvD;IAEA,mBAAmB,CAAC,WAAqB,QAAQ,AAAC,eAAuB,OAAT,UAAS;IAEzE,yBAAyB;IACzB,qBAAqB,CAAC,QAAmB,QAAQ,AAAC,qBAAmD,OAA/B,QAAQ,AAAC,UAAe,OAAN,SAAU;IAElG,2BAA2B,CAAC,WAAqB,QAAQ,AAAC,6BAAqC,OAAT;IAEtF,oBAAoB,CAAC,WAAmB,QAAkB,QAAQ,AAAC,sBAA+B,OAAV,WAAU,WAAS;YACvG,QAAQ;YACR,MAAM;gBAAE;YAAM;QAClB;IAEA,eAAe,CAAC,YAAsB,QAAQ,AAAC,sBAA+B,OAAV,YAAa;YAC7E,QAAQ;QACZ;IAEA,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAAoB,QAAQ,sBAAsB;YAC9E,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC9B;IAEA,cAAc,CAAC,WAAqB,QAAQ,kBAAkB;YAC1D,QAAQ;YACR,MAAM;gBAAE;YAAS;QACrB;IAEA,aAAa,CAAC,WAAqB,QAAQ,AAAC,uBAA+B,OAAT;IAElE,oCAAoC;IACpC,cAAc,CAAC;QACX,OAAO,MAAM,AAAC,GAAU,OAAR,SAAQ,iBAAe;YACnC,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;IAEA,aAAa,IAAM,QAAQ;IAE3B,WAAW,CAAC,WAAqB,QAAQ,AAAC,gBAAwB,OAAT;IAEzD,cAAc,CAAC,UAAkB,YAAiB,mBAAiC;QAC/E,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC3D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACpD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACvD,OAAO;oBACH,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBACjD;YACJ;QACJ;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACnB,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,IAAI,WAAW;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,OAAO,MAAM,AAAC,GAAyB,OAAvB,SAAQ,iBAAwB,OAAT,WAAY;YAC/C,QAAQ;YACR,SAAS;gBACL,iBAAiB,AAAC,UAAe,OAAN;YAC/B;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YAC/E;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/chat/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { Avatar } from \"@/components/ui/avatar\";\r\nimport { Send, Loader2, ArrowLeft } from \"lucide-react\";\r\nimport { api } from \"@/lib/api\";\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\ninterface Message {\r\n  role: \"user\" | \"assistant\";\r\n  content: string;\r\n}\r\n\r\nexport default function ChatPage() {\r\n  const searchParams = useSearchParams();\r\n  const expertId = searchParams.get(\"expertId\");\r\n  const threadId = searchParams.get(\"threadId\");\r\n  \r\n  const [expert, setExpert] = useState<Expert | null>(null);\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);\r\n  const [currentSessionId, setCurrentSessionId] = useState<number | null>(null);\r\n  const [isLoadingExpert, setIsLoadingExpert] = useState(!!expertId);\r\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\r\n  const chatRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (expertId) {\r\n      initializeChatWithExpert();\r\n    } else if (threadId) {\r\n      // Direct thread access (legacy support)\r\n      loadChatHistory();\r\n    }\r\n  }, [expertId, threadId]);\r\n\r\n  const initializeChatWithExpert = async () => {\r\n    try {\r\n      setIsLoadingExpert(true);\r\n      setIsLoadingHistory(true);\r\n      \r\n      // First load expert information\r\n      const expertResult = await api.getExpert(expertId!);\r\n      if (!expertResult.success) {\r\n        console.error('Failed to load expert:', expertResult);\r\n        return;\r\n      }\r\n      \r\n      setExpert(expertResult.expert);\r\n      \r\n      // Check for existing active session for this expert\r\n      const sessionResult = await api.getActiveSessionForExpert(expertId!);\r\n      \r\n      if (sessionResult.success && sessionResult.session) {\r\n        // Found existing session - load its history using session ID\r\n        const existingSession = sessionResult.session;\r\n        setCurrentThreadId(existingSession.thread_id);\r\n        setCurrentSessionId(existingSession.id);\r\n        \r\n        // Load chat history for this session using the more reliable method\r\n        await loadChatHistoryBySessionId(existingSession.id);\r\n        \r\n        console.log('Loaded existing session:', existingSession.id, 'with thread:', existingSession.thread_id);\r\n      } else {\r\n        // No existing session - show welcome message for new chat\r\n        setMessages([{\r\n          role: \"assistant\",\r\n          content: `Hello! I'm ${expertResult.expert.name}. ${expertResult.expert.description} How can I assist you today?`\r\n        }]);\r\n        console.log('No existing session found, starting fresh chat');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to initialize chat with expert:', error);\r\n    } finally {\r\n      setIsLoadingExpert(false);\r\n      setIsLoadingHistory(false);\r\n    }\r\n  };\r\n\r\n  const loadChatHistory = async () => {\r\n    if (!threadId) return;\r\n    await loadChatHistoryForThread(threadId);\r\n  };\r\n\r\n  const loadChatHistoryForThread = async (targetThreadId: string) => {\r\n    try {\r\n      setIsLoadingHistory(true);\r\n      const result = await api.getThreadMessages(targetThreadId);\r\n      if (result.success && result.messages) {\r\n        const formattedMessages = result.messages.map((msg: any) => ({\r\n          role: msg.role,\r\n          content: msg.content\r\n        }));\r\n        setMessages(formattedMessages);\r\n        \r\n        // If we don't have expert info but the thread has expert context, try to load it\r\n        if (!expert && result.messages.length > 0) {\r\n          // You might want to get expert info from the first message or session data\r\n          // For now, we'll just show the messages\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load chat history:', error);\r\n    } finally {\r\n      setIsLoadingHistory(false);\r\n    }\r\n  };\r\n\r\n  const loadChatHistoryBySessionId = async (sessionId: number) => {\r\n    try {\r\n      setIsLoadingHistory(true);\r\n      // For now, we'll use the existing API which expects threadId\r\n      // Later we can add a new API endpoint for sessionId-based loading\r\n      const result = await api.getUserChatSessions(1); // Get the specific session\r\n      // This is a temporary workaround - we should add a proper sessionId-based API\r\n      // For now, let's just load by threadId\r\n      if (currentThreadId) {\r\n        await loadChatHistoryForThread(currentThreadId);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load chat history by session:', error);\r\n    } finally {\r\n      setIsLoadingHistory(false);\r\n    }\r\n  };\r\n\r\n  const getExpertIcon = (labels: string[]) => {\r\n    if (!labels) return '🤖';\r\n    if (labels.includes('business') || labels.includes('marketing')) return '💼';\r\n    if (labels.includes('code') || labels.includes('programming')) return '💻';\r\n    if (labels.includes('creative') || labels.includes('design')) return '🎨';\r\n    if (labels.includes('education') || labels.includes('learning')) return '📚';\r\n    if (labels.includes('health') || labels.includes('medical')) return '🏥';\r\n    if (labels.includes('finance') || labels.includes('money')) return '💰';\r\n    return '🤖';\r\n  };\r\n\r\n  const sendMessage = useMutation({\r\n    mutationFn: async ({ message, threadId: tId }: { message: string; threadId: string | null }) => {\r\n      setIsLoading(true);\r\n\r\n      const res = await fetch(\"/api/chat\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ \r\n          message, \r\n          threadId: tId,\r\n          expertId: expertId,\r\n          expertContext: expert ? {\r\n            name: expert.name,\r\n            systemPrompt: expert.systemPrompt,\r\n            model: expert.model\r\n          } : undefined\r\n        }),\r\n      });\r\n\r\n      if (!res.ok) {\r\n        throw new Error(`HTTP error! status: ${res.status}`);\r\n      }\r\n\r\n      const data = await res.json();\r\n\r\n      // Update thread ID if we got a new one\r\n      if (data.threadId) {\r\n        setCurrentThreadId(data.threadId);\r\n      }\r\n\r\n      // Add the AI response to messages\r\n      if (data.response) {\r\n        setMessages((msgs) => [...msgs, { role: \"assistant\", content: data.response }]);\r\n      }\r\n\r\n      setIsLoading(false);\r\n      return data;\r\n    },\r\n    onError: () => {\r\n      setIsLoading(false);\r\n    }\r\n  });\r\n\r\n  const handleSend = () => {\r\n    if (!input.trim() || isLoading) return;\r\n    setMessages((msgs) => [...msgs, { role: \"user\", content: input }]);\r\n    sendMessage.mutate({ message: input, threadId: currentThreadId });\r\n    setInput(\"\");\r\n    setTimeout(() => {\r\n      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: \"smooth\" });\r\n    }, 100);\r\n  };\r\n\r\n  if (isLoadingExpert || isLoadingHistory) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\" style={{ borderColor: '#1E3A8A' }}></div>\r\n          <p className=\"text-gray-600\">Loading expert...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <Link \r\n              href={expert ? `/expert/${expert.id}` : \"/\"}\r\n              className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n            >\r\n              <ArrowLeft className=\"w-4 h-4\" />\r\n              <span>{expert ? 'Back to Profile' : 'Back to Home'}</span>\r\n            </Link>\r\n            \r\n            {expert && (\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"text-right\">\r\n                  <p className=\"text-sm text-gray-500\">Chatting with</p>\r\n                  <p className=\"font-semibold text-gray-900\">{expert.name}</p>\r\n                </div>\r\n                {expert.imageUrl ? (\r\n                  <img \r\n                    src={`http://localhost:3001${expert.imageUrl}`} \r\n                    alt={expert.name}\r\n                    className=\"w-10 h-10 object-cover rounded-full border-2 border-gray-200\"\r\n                  />\r\n                ) : (\r\n                  <div \r\n                    className=\"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm\"\r\n                    style={{ backgroundColor: '#1E3A8A' }}\r\n                  >\r\n                    {getExpertIcon(expert.labels)}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Chat Interface */}\r\n        <Card className=\"bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl\">\r\n          <div className=\"p-6\">\r\n            {/* Expert Info Banner */}\r\n            {expert && (\r\n              <div className=\"mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  {expert.imageUrl ? (\r\n                    <img \r\n                      src={`http://localhost:3001${expert.imageUrl}`} \r\n                      alt={expert.name}\r\n                      className=\"w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm\"\r\n                    />\r\n                  ) : (\r\n                    <div \r\n                      className=\"w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm\"\r\n                      style={{ backgroundColor: '#1E3A8A' }}\r\n                    >\r\n                      {getExpertIcon(expert.labels)}\r\n                    </div>\r\n                  )}\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"font-semibold text-gray-900\">{expert.name}</h3>\r\n                    <p className=\"text-sm text-gray-600\">{expert.description}</p>\r\n                    <div className=\"flex items-center space-x-2 mt-1\">\r\n                      <span className=\"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full\">\r\n                        {expert.model}\r\n                      </span>\r\n                      <span className=\"text-xs text-green-600\">● Online</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Chat Messages */}\r\n            <div \r\n              ref={chatRef} \r\n              className=\"h-[60vh] overflow-y-auto space-y-4 mb-6 px-2\"\r\n              style={{ scrollbarWidth: 'thin' }}\r\n            >\r\n              {messages.length === 0 && !expert && (\r\n                <div className=\"text-center mt-20\">\r\n                  <div className=\"text-6xl mb-4\">💬</div>\r\n                  <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">Start a Conversation</h3>\r\n                  <p className=\"text-gray-500\">Choose an expert from the marketplace or start chatting!</p>\r\n                </div>\r\n              )}\r\n              \r\n              {messages.map((msg, i) => (\r\n                <div key={i} className={`flex gap-3 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}>\r\n                  {msg.role === \"assistant\" && (\r\n                    <div className=\"flex-shrink-0\">\r\n                      {expert?.imageUrl ? (\r\n                        <img \r\n                          src={`http://localhost:3001${expert.imageUrl}`} \r\n                          alt={expert.name}\r\n                          className=\"w-8 h-8 object-cover rounded-full border border-gray-200\"\r\n                        />\r\n                      ) : (\r\n                        <div \r\n                          className=\"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm\"\r\n                          style={{ backgroundColor: '#1E3A8A' }}\r\n                        >\r\n                          {expert ? getExpertIcon(expert.labels) : '🤖'}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                  \r\n                  <div className={`rounded-2xl px-4 py-3 max-w-[70%] ${\r\n                    msg.role === \"user\" \r\n                      ? \"text-white shadow-lg\" \r\n                      : \"bg-gray-50 text-gray-900 border border-gray-100\"\r\n                  }`}\r\n                  style={msg.role === \"user\" ? { backgroundColor: '#1E3A8A' } : {}}\r\n                  >\r\n                    <div \r\n                      className=\"text-sm leading-relaxed\"\r\n                      dangerouslySetInnerHTML={{ __html: msg.content.replace(/\\n/g, '<br/>') }} \r\n                    />\r\n                  </div>\r\n                  \r\n                  {msg.role === \"user\" && (\r\n                    <div className=\"flex-shrink-0\">\r\n                      <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                        <span className=\"text-sm\">👤</span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              \r\n              {isLoading && (\r\n                <div className=\"flex gap-3 justify-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    {expert?.imageUrl ? (\r\n                      <img \r\n                        src={`http://localhost:3001${expert.imageUrl}`} \r\n                        alt={expert.name}\r\n                        className=\"w-8 h-8 object-cover rounded-full border border-gray-200\"\r\n                      />\r\n                    ) : (\r\n                      <div \r\n                        className=\"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm\"\r\n                        style={{ backgroundColor: '#1E3A8A' }}\r\n                      >\r\n                        {expert ? getExpertIcon(expert.labels) : '🤖'}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"rounded-2xl px-4 py-3 bg-gray-50 border border-gray-100 flex items-center gap-2\">\r\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\r\n                    <span className=\"text-sm text-gray-600\">AI is thinking...</span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Input Area */}\r\n            <form \r\n              className=\"flex gap-3 items-end\" \r\n              onSubmit={e => { e.preventDefault(); handleSend(); }}\r\n            >\r\n              <div className=\"flex-1\">\r\n                <Input\r\n                  value={input}\r\n                  onChange={e => setInput(e.target.value)}\r\n                  placeholder={expert ? `Ask ${expert.name} anything...` : \"Type your message...\"}\r\n                  disabled={isLoading}\r\n                  className=\"w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none\"\r\n                  autoFocus\r\n                  onKeyDown={e => { \r\n                    if (e.key === \"Enter\" && !e.shiftKey) { \r\n                      e.preventDefault(); \r\n                      handleSend(); \r\n                    } \r\n                  }}\r\n                />\r\n              </div>\r\n              <Button \r\n                type=\"submit\" \r\n                disabled={isLoading || !input.trim()} \r\n                className=\"px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg\"\r\n                style={{ backgroundColor: '#1E3A8A' }}\r\n              >\r\n                {isLoading ? (\r\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\r\n                ) : (\r\n                  <Send className=\"w-5 h-5\" />\r\n                )}\r\n              </Button>\r\n            </form>\r\n\r\n            {/* Usage Info */}\r\n            {expert && (\r\n              <div className=\"mt-4 text-center\">\r\n                <p className=\"text-xs text-gray-500\">\r\n                  💡 You're chatting with {expert.name} • Usage fee: {expert.pricingPercentage}% of tokens\r\n                  {currentThreadId && (\r\n                    <span className=\"ml-2\">• Thread: {currentThreadId.substring(0, 8)}...</span>\r\n                  )}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;;;AAXA;;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,MAAM,WAAW,aAAa,GAAG,CAAC;IAElC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,UAAU;gBACZ;YACF,OAAO,IAAI,UAAU;gBACnB,wCAAwC;gBACxC;YACF;QACF;6BAAG;QAAC;QAAU;KAAS;IAEvB,MAAM,2BAA2B;QAC/B,IAAI;YACF,mBAAmB;YACnB,oBAAoB;YAEpB,gCAAgC;YAChC,MAAM,eAAe,MAAM,oHAAA,CAAA,MAAG,CAAC,SAAS,CAAC;YACzC,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,QAAQ,KAAK,CAAC,0BAA0B;gBACxC;YACF;YAEA,UAAU,aAAa,MAAM;YAE7B,oDAAoD;YACpD,MAAM,gBAAgB,MAAM,oHAAA,CAAA,MAAG,CAAC,yBAAyB,CAAC;YAE1D,IAAI,cAAc,OAAO,IAAI,cAAc,OAAO,EAAE;gBAClD,6DAA6D;gBAC7D,MAAM,kBAAkB,cAAc,OAAO;gBAC7C,mBAAmB,gBAAgB,SAAS;gBAC5C,oBAAoB,gBAAgB,EAAE;gBAEtC,oEAAoE;gBACpE,MAAM,2BAA2B,gBAAgB,EAAE;gBAEnD,QAAQ,GAAG,CAAC,4BAA4B,gBAAgB,EAAE,EAAE,gBAAgB,gBAAgB,SAAS;YACvG,OAAO;gBACL,0DAA0D;gBAC1D,YAAY;oBAAC;wBACX,MAAM;wBACN,SAAS,AAAC,cAA0C,OAA7B,aAAa,MAAM,CAAC,IAAI,EAAC,MAAoC,OAAhC,aAAa,MAAM,CAAC,WAAW,EAAC;oBACtF;iBAAE;gBACF,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D,SAAU;YACR,mBAAmB;YACnB,oBAAoB;QACtB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QACf,MAAM,yBAAyB;IACjC;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC;YAC3C,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,EAAE;gBACrC,MAAM,oBAAoB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC3D,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,YAAY;gBAEZ,iFAAiF;gBACjF,IAAI,CAAC,UAAU,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACzC,2EAA2E;gBAC3E,wCAAwC;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,6BAA6B,OAAO;QACxC,IAAI;YACF,oBAAoB;YACpB,6DAA6D;YAC7D,kEAAkE;YAClE,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,mBAAmB,CAAC,IAAI,2BAA2B;YAC5E,8EAA8E;YAC9E,uCAAuC;YACvC,IAAI,iBAAiB;gBACnB,MAAM,yBAAyB;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,QAAQ,OAAO;QACpB,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,cAAc,OAAO;QACxE,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,gBAAgB,OAAO;QACtE,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,WAAW,OAAO;QACrE,IAAI,OAAO,QAAQ,CAAC,gBAAgB,OAAO,QAAQ,CAAC,aAAa,OAAO;QACxE,IAAI,OAAO,QAAQ,CAAC,aAAa,OAAO,QAAQ,CAAC,YAAY,OAAO;QACpE,IAAI,OAAO,QAAQ,CAAC,cAAc,OAAO,QAAQ,CAAC,UAAU,OAAO;QACnE,OAAO;IACT;IAEA,MAAM,cAAc,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,UAAU;iDAAE;oBAAO,EAAE,OAAO,EAAE,UAAU,GAAG,EAAgD;gBACzF,aAAa;gBAEb,MAAM,MAAM,MAAM,MAAM,aAAa;oBACnC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA,UAAU;wBACV,UAAU;wBACV,eAAe,SAAS;4BACtB,MAAM,OAAO,IAAI;4BACjB,cAAc,OAAO,YAAY;4BACjC,OAAO,OAAO,KAAK;wBACrB,IAAI;oBACN;gBACF;gBAEA,IAAI,CAAC,IAAI,EAAE,EAAE;oBACX,MAAM,IAAI,MAAM,AAAC,uBAAiC,OAAX,IAAI,MAAM;gBACnD;gBAEA,MAAM,OAAO,MAAM,IAAI,IAAI;gBAE3B,uCAAuC;gBACvC,IAAI,KAAK,QAAQ,EAAE;oBACjB,mBAAmB,KAAK,QAAQ;gBAClC;gBAEA,kCAAkC;gBAClC,IAAI,KAAK,QAAQ,EAAE;oBACjB;6DAAY,CAAC,OAAS;mCAAI;gCAAM;oCAAE,MAAM;oCAAa,SAAS,KAAK,QAAQ;gCAAC;6BAAE;;gBAChF;gBAEA,aAAa;gBACb,OAAO;YACT;;QACA,OAAO;iDAAE;gBACP,aAAa;YACf;;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAChC,YAAY,CAAC,OAAS;mBAAI;gBAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAM;aAAE;QACjE,YAAY,MAAM,CAAC;YAAE,SAAS;YAAO,UAAU;QAAgB;QAC/D,SAAS;QACT,WAAW;gBACT;aAAA,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,QAAQ,CAAC;gBAAE,KAAK,QAAQ,OAAO,CAAC,YAAY;gBAAE,UAAU;YAAS;QACpF,GAAG;IACL;IAEA,IAAI,mBAAmB,kBAAkB;QACvC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA8D,OAAO;4BAAE,aAAa;wBAAU;;;;;;kCAC7G,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,SAAS,AAAC,WAAoB,OAAV,OAAO,EAAE,IAAK;gCACxC,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,SAAS,oBAAoB;;;;;;;;;;;;4BAGrC,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAA+B,OAAO,IAAI;;;;;;;;;;;;oCAExD,OAAO,QAAQ,iBACd,6LAAC;wCACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;wCAC5C,KAAK,OAAO,IAAI;wCAChB,WAAU;;;;;6DAGZ,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAU;kDAEnC,cAAc,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BASxC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,QAAQ,iBACd,6LAAC;4CACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;4CAC5C,KAAK,OAAO,IAAI;4CAChB,WAAU;;;;;iEAGZ,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAU;sDAEnC,cAAc,OAAO,MAAM;;;;;;sDAGhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+B,OAAO,IAAI;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;8DACxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,OAAO,KAAK;;;;;;sEAEf,6LAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQnD,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;oCAE/B,SAAS,MAAM,KAAK,KAAK,CAAC,wBACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;oCAIhC,SAAS,GAAG,CAAC,CAAC,KAAK,kBAClB,6LAAC;4CAAY,WAAW,AAAC,cAAmE,OAAtD,IAAI,IAAI,KAAK,SAAS,gBAAgB;;gDACzE,IAAI,IAAI,KAAK,6BACZ,6LAAC;oDAAI,WAAU;8DACZ,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,kBACf,6LAAC;wDACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;wDAC5C,KAAK,OAAO,IAAI;wDAChB,WAAU;;;;;6EAGZ,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAU;kEAEnC,SAAS,cAAc,OAAO,MAAM,IAAI;;;;;;;;;;;8DAMjD,6LAAC;oDAAI,WAAW,AAAC,qCAIhB,OAHC,IAAI,IAAI,KAAK,SACT,yBACA;oDAEN,OAAO,IAAI,IAAI,KAAK,SAAS;wDAAE,iBAAiB;oDAAU,IAAI,CAAC;8DAE7D,cAAA,6LAAC;wDACC,WAAU;wDACV,yBAAyB;4DAAE,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO;wDAAS;;;;;;;;;;;gDAI1E,IAAI,IAAI,KAAK,wBACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;2CApCxB;;;;;oCA2CX,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,kBACf,6LAAC;oDACC,KAAK,AAAC,wBAAuC,OAAhB,OAAO,QAAQ;oDAC5C,KAAK,OAAO,IAAI;oDAChB,WAAU;;;;;yEAGZ,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAU;8DAEnC,SAAS,cAAc,OAAO,MAAM,IAAI;;;;;;;;;;;0DAI/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAOhD,6LAAC;gCACC,WAAU;gCACV,UAAU,CAAA;oCAAO,EAAE,cAAc;oCAAI;gCAAc;;kDAEnD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;4CACtC,aAAa,SAAS,AAAC,OAAkB,OAAZ,OAAO,IAAI,EAAC,kBAAgB;4CACzD,UAAU;4CACV,WAAU;4CACV,SAAS;4CACT,WAAW,CAAA;gDACT,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oDACpC,EAAE,cAAc;oDAChB;gDACF;4CACF;;;;;;;;;;;kDAGJ,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,aAAa,CAAC,MAAM,IAAI;wCAClC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAU;kDAEnC,0BACC,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAMrB,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAAwB;wCACV,OAAO,IAAI;wCAAC;wCAAe,OAAO,iBAAiB;wCAAC;wCAC5E,iCACC,6LAAC;4CAAK,WAAU;;gDAAO;gDAAW,gBAAgB,SAAS,CAAC,GAAG;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtF;GA9YwB;;QACD,qIAAA,CAAA,kBAAe;QA4HhB,iLAAA,CAAA,cAAW;;;KA7HT", "debugId": null}}]}