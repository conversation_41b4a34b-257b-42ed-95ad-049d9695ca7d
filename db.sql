-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 25, 2025 at 08:47 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

--
-- Database: `aitrainerhub`
--

-- --------------------------------------------------------

--
-- Table structure for table `chat_messages`
--

CREATE TABLE `chat_messages` (
  `id` int NOT NULL,
  `session_id` int NOT NULL,
  `thread_id` varchar(255) NOT NULL,
  `role` enum('user','assistant') NOT NULL,
  `content` text NOT NULL,
  `message_order` int NOT NULL,
  `tokens_used` int DEFAULT '0',
  `cost` decimal(10,6) DEFAULT '0.000000',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `chat_sessions`
--

CREATE TABLE `chat_sessions` (
  `id` int NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `thread_id` varchar(255) NOT NULL,
  `expert_id` int DEFAULT NULL,
  `expert_name` varchar(255) DEFAULT NULL,
  `expert_model` varchar(100) DEFAULT NULL,
  `session_title` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `chat_sessions_with_stats`
-- (See below for the actual view)
--
CREATE TABLE `chat_sessions_with_stats` (
`id` int
,`user_id` varchar(255)
,`thread_id` varchar(255)
,`expert_id` int
,`expert_name` varchar(255)
,`expert_model` varchar(100)
,`session_title` varchar(255)
,`created_at` timestamp
,`updated_at` timestamp
,`is_active` tinyint(1)
,`message_count` bigint
,`last_message_at` timestamp
,`user_messages` decimal(23,0)
,`assistant_messages` decimal(23,0)
,`total_tokens` decimal(32,0)
,`total_cost` decimal(32,6)
);

-- --------------------------------------------------------

--
-- Table structure for table `experts`
--

CREATE TABLE `experts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `system_prompt` text NOT NULL,
  `model` varchar(100) DEFAULT 'gpt-4o-mini',
  `assistant_id` varchar(255) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `pricing_percentage` decimal(5,2) DEFAULT '0.00',
  `is_public` tinyint(1) DEFAULT '0',
  `labels` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `experts`
--

INSERT INTO `experts` (`id`, `user_id`, `name`, `description`, `system_prompt`, `model`, `assistant_id`, `image_url`, `pricing_percentage`, `is_public`, `labels`, `created_at`, `updated_at`) VALUES
(1, 1, 'klikbuy4', 'klikbuy 4', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DlXl1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klibuy\", \"marketing\"]', '2025-07-25 06:03:46', '2025-07-25 06:20:26'),
(42, 1, 'klikbuy4', 'klikbuy 4', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:03:46', '2025-07-25 06:20:32'),
(43, 1, 'klikbuy5', 'klikbuy 5', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:00', '2025-07-25 06:38:45'),
(44, 1, 'klikbuy6', 'klikbuy 6', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:10', '2025-07-25 06:38:51'),
(45, 1, 'klikbuy7', 'klikbuy 7', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:20', '2025-07-25 06:38:51'),
(46, 1, 'klikbuy8', 'klikbuy 8', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:30', '2025-07-25 06:38:51'),
(47, 1, 'klikbuy9', 'klikbuy 9', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:40', '2025-07-25 06:38:51'),
(48, 1, 'klikbuy10', 'klikbuy 10', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:04:50', '2025-07-25 06:38:51'),
(49, 1, 'klikbuy11', 'klikbuy 11', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:00', '2025-07-25 06:38:51'),
(50, 1, 'klikbuy12', 'klikbuy 12', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:10', '2025-07-25 06:38:51'),
(51, 1, 'klikbuy13', 'klikbuy 13', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:20', '2025-07-25 06:38:51'),
(52, 1, 'klikbuy14', 'klikbuy 14', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:30', '2025-07-25 06:38:51'),
(53, 1, 'klikbuy15', 'klikbuy 15', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:40', '2025-07-25 06:38:51'),
(54, 1, 'klikbuy16', 'klikbuy 16', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:05:50', '2025-07-25 06:38:51'),
(55, 1, 'klikbuy17', 'klikbuy 17', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:00', '2025-07-25 06:38:51'),
(56, 1, 'klikbuy18', 'klikbuy 18', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:10', '2025-07-25 06:38:51'),
(57, 1, 'klikbuy19', 'klikbuy 19', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:20', '2025-07-25 06:38:51'),
(58, 1, 'klikbuy20', 'klikbuy 20', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:30', '2025-07-25 06:38:51'),
(59, 1, 'klikbuy21', 'klikbuy 21', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:40', '2025-07-25 06:38:51'),
(60, 1, 'klikbuy22', 'klikbuy 22', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:06:50', '2025-07-25 06:38:51'),
(61, 1, 'klikbuy23', 'klikbuy 23', 'terangkan apa itu klikbuy', 'gpt-4o-mini', 'asst_Lu5wTwhb9DXI1hJRDukbc6d', '/uploads/image-1753423415732-240870668.jpeg', 100.00, 1, '[\"klikbuy\", \"marketing\"]', '2025-07-25 06:07:00', '2025-07-25 06:38:51');

-- --------------------------------------------------------

--
-- Table structure for table `experts3`
--

CREATE TABLE `experts3` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `system_prompt` text NOT NULL,
  `model` varchar(100) DEFAULT 'gpt-4o-mini',
  `assistant_id` varchar(255) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `pricing_percentage` decimal(5,2) DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `experts3`
--

INSERT INTO `experts3` (`id`, `user_id`, `name`, `description`, `system_prompt`, `model`, `assistant_id`, `image_url`, `pricing_percentage`, `created_at`, `updated_at`) VALUES
(1, 1, 'test klikbuy1', 'test klikbuy1', 'kamu adalah support klikbuy', 'gpt-4o-mini', 'asst_tmp18mwS3LixhqntKGqe4285', NULL, 0.00, '2025-07-25 05:02:05', '2025-07-25 05:02:05'),
(2, 1, 'test klikbuy2', 'test klikbuy2', 'kamu adalah support klikbuy', 'gpt-4o-mini', 'asst_tajj2dDOAo3X5n8vkZ6yNRkZ', '/uploads/image-1753420474908-455498775.jpeg', 100.00, '2025-07-25 05:14:40', '2025-07-25 05:14:40');

-- --------------------------------------------------------

--
-- Stand-in structure for view `recent_chat_sessions`
-- (See below for the actual view)
--
CREATE TABLE `recent_chat_sessions` (
`id` int
,`user_id` varchar(255)
,`thread_id` varchar(255)
,`expert_id` int
,`expert_name` varchar(255)
,`expert_model` varchar(100)
,`session_title` varchar(255)
,`created_at` timestamp
,`updated_at` timestamp
,`is_active` tinyint(1)
,`message_count` bigint
,`last_message_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`user_id`, `token`, `nama`, `created_at`, `updated_at`) VALUES
(1, 'abcde', 'Pile', '2025-07-25 04:10:57', '2025-07-25 04:11:09');

-- --------------------------------------------------------

--
-- Table structure for table `user_chat_stats`
--

CREATE TABLE `user_chat_stats` (
  `id` int NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `total_sessions` int DEFAULT '0',
  `total_messages` int DEFAULT '0',
  `total_tokens_used` int DEFAULT '0',
  `total_cost` decimal(10,2) DEFAULT '0.00',
  `last_chat_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure for view `chat_sessions_with_stats`
--
DROP TABLE IF EXISTS `chat_sessions_with_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `chat_sessions_with_stats`  AS SELECT `cs`.`id` AS `id`, `cs`.`user_id` AS `user_id`, `cs`.`thread_id` AS `thread_id`, `cs`.`expert_id` AS `expert_id`, `cs`.`expert_name` AS `expert_name`, `cs`.`expert_model` AS `expert_model`, `cs`.`session_title` AS `session_title`, `cs`.`created_at` AS `created_at`, `cs`.`updated_at` AS `updated_at`, `cs`.`is_active` AS `is_active`, count(`cm`.`id`) AS `message_count`, max(`cm`.`created_at`) AS `last_message_at`, sum((case when (`cm`.`role` = 'user') then 1 else 0 end)) AS `user_messages`, sum((case when (`cm`.`role` = 'assistant') then 1 else 0 end)) AS `assistant_messages`, sum(`cm`.`tokens_used`) AS `total_tokens`, sum(`cm`.`cost`) AS `total_cost` FROM (`chat_sessions` `cs` left join `chat_messages` `cm` on((`cs`.`id` = `cm`.`session_id`))) GROUP BY `cs`.`id` ;

-- --------------------------------------------------------

--
-- Structure for view `recent_chat_sessions`
--
DROP TABLE IF EXISTS `recent_chat_sessions`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `recent_chat_sessions`  AS SELECT `cs`.`id` AS `id`, `cs`.`user_id` AS `user_id`, `cs`.`thread_id` AS `thread_id`, `cs`.`expert_id` AS `expert_id`, `cs`.`expert_name` AS `expert_name`, `cs`.`expert_model` AS `expert_model`, `cs`.`session_title` AS `session_title`, `cs`.`created_at` AS `created_at`, `cs`.`updated_at` AS `updated_at`, `cs`.`is_active` AS `is_active`, count(`cm`.`id`) AS `message_count`, max(`cm`.`created_at`) AS `last_message_at` FROM (`chat_sessions` `cs` left join `chat_messages` `cm` on((`cs`.`id` = `cm`.`session_id`))) WHERE (`cs`.`is_active` = true) GROUP BY `cs`.`id` ORDER BY `last_message_at` DESC, `cs`.`updated_at` DESC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_thread_id` (`thread_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_role` (`role`);

--
-- Indexes for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `thread_id` (`thread_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_thread_id` (`thread_id`),
  ADD KEY `idx_expert_id` (`expert_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `experts`
--
ALTER TABLE `experts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `experts3`
--
ALTER TABLE `experts3`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `token` (`token`);

--
-- Indexes for table `user_chat_stats`
--
ALTER TABLE `user_chat_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `experts`
--
ALTER TABLE `experts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `experts3`
--
ALTER TABLE `experts3`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `user_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT for table `user_chat_stats`
--
ALTER TABLE `user_chat_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  ADD CONSTRAINT `chat_sessions_ibfk_1` FOREIGN KEY (`expert_id`) REFERENCES `experts` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `experts`
--
ALTER TABLE `experts`
  ADD CONSTRAINT `experts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `experts3`
--
ALTER TABLE `experts3`
  ADD CONSTRAINT `experts3_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;
COMMIT;
