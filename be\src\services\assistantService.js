const openai = require('../config/openai');
const fs = require('fs');

class AssistantService {
  async uploadFile(filePath, fileName) {
    try {
      const file = await openai.files.create({
        file: fs.createReadStream(filePath),
        purpose: 'assistants'
      });

      return file.id;
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  async waitForVectorStoreReady(vectorStoreId, vectorStoresAPI = null) {
    const api = vectorStoresAPI || openai.vectorStores || openai.beta.vectorStores;
    let vectorStore = await api.retrieve(vectorStoreId);

    while (vectorStore.status === 'in_progress') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      vectorStore = await api.retrieve(vectorStoreId);
    }

    return vectorStore;
  }

  async createAssistant(assistantData, fileId = null) {
    try {
      // Debug: Check what's available in openai.beta and main openai
      console.log('Available in openai.beta:', Object.keys(openai.beta));
      console.log('Available in openai:', Object.keys(openai));
      console.log('Checking vectorStores in different locations:');
      console.log('- openai.vectorStores:', !!openai.vectorStores);
      console.log('- openai.beta.vectorStores:', !!openai.beta.vectorStores);

      // Step 1: Create assistant with file_search tool if file is provided
      const assistantConfig = {
        name: assistantData.name,
        instructions: assistantData.instructions,
        model: assistantData.model || 'gpt-4o-mini',
        tools: []
      };

      // Add file_search tool if file is provided
      if (fileId) {
        assistantConfig.tools.push({ type: 'file_search' });
      }

      const assistant = await openai.beta.assistants.create(assistantConfig);

      // Step 2: If file provided, try to create vector store and upload file
      if (fileId) {
        console.log('File ID provided:', fileId);
        try {
          // Check if vectorStores API is available in different locations
          const vectorStoresAPI = openai.vectorStores || openai.beta.vectorStores;
          console.log('VectorStores API found:', !!vectorStoresAPI);

          if (vectorStoresAPI) {
            console.log('Creating vector store...');
            // Create a vector store
            const vectorStore = await vectorStoresAPI.create({
              name: `${assistantData.name} Knowledge Base`
            });
            console.log('Vector store created:', vectorStore.id);

            console.log('Adding file to vector store...');
            // Add file to vector store
            await vectorStoresAPI.files.create(vectorStore.id, {
              file_id: fileId
            });

            console.log('Waiting for vector store to be ready...');
            // Wait for vector store to be ready (update method call)
            await this.waitForVectorStoreReady(vectorStore.id, vectorStoresAPI);

            console.log('Updating assistant with vector store...');
            // Step 3: Update assistant to use the vector store
            await openai.beta.assistants.update(assistant.id, {
              tool_resources: {
                file_search: {
                  vector_store_ids: [vectorStore.id]
                }
              }
            });
            console.log('Assistant updated successfully');
          } else {
            console.warn('Vector Stores API not available, creating assistant without file attachment');
          }
        } catch (vectorError) {
          console.error('Vector store creation failed:', vectorError);
          console.error('Error details:', vectorError.message);
          // Continue without vector store - assistant will still be created
        }
      }

      // Get updated assistant info
      const updatedAssistant = await openai.beta.assistants.retrieve(assistant.id);

      return {
        id: updatedAssistant.id,
        name: updatedAssistant.name,
        instructions: updatedAssistant.instructions,
        model: updatedAssistant.model,
        tools: updatedAssistant.tools,
        tool_resources: updatedAssistant.tool_resources || {}
      };
    } catch (error) {
      console.error('Assistant creation error:', error);
      throw new Error(`Failed to create assistant: ${error.message}`);
    }
  }

  async processAssistantCreation(assistantData, file = null) {
    try {
      let fileId = null;

      // Upload file if provided
      if (file) {
        fileId = await this.uploadFile(file.path, file.originalname);

        // Clean up temporary file
        fs.unlinkSync(file.path);
      }

      // Create assistant
      const assistant = await this.createAssistant(assistantData, fileId);

      return {
        success: true,
        assistant
      };
    } catch (error) {
      // Clean up temporary file if it exists
      if (file && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  }

  validateAssistantData(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.instructions || data.instructions.trim() === '') {
      errors.push('Instructions are required');
    }

    if (data.model && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    return errors;
  }

  isValidModel(model) {
    const validModels = [
      'gpt-3.5-turbo',
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4-turbo-preview',
      'gpt-4o',
      'gpt-4o-mini'
    ];
    return validModels.includes(model);
  }

  isValidFileType(mimetype, filename) {
    const validMimeTypes = [
      'application/pdf',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/markdown',
      'application/json'
    ];

    const validExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));

    return validMimeTypes.includes(mimetype) || validExtensions.includes(fileExtension);
  }

  async updateAssistantInstructions(assistantId, instructions) {
    try {
      const assistant = await openai.beta.assistants.update(assistantId, {
        instructions: instructions
      });

      return {
        success: true,
        assistant: assistant
      };
    } catch (error) {
      console.error('Update assistant instructions error:', error);
      throw new Error(`Failed to update assistant instructions: ${error.message}`);
    }
  }

  async updateAssistantKnowledgeBase(assistantId, knowledgeBaseFile) {
    try {
      // Upload new file
      const fileId = await this.uploadFile(knowledgeBaseFile.path, knowledgeBaseFile.originalname);

      // Get current assistant to check existing vector store
      const currentAssistant = await openai.beta.assistants.retrieve(assistantId);
      
      let vectorStoreId = null;
      
      // Check if assistant already has a vector store
      if (currentAssistant.tool_resources?.file_search?.vector_store_ids?.length > 0) {
        vectorStoreId = currentAssistant.tool_resources.file_search.vector_store_ids[0];
        
        // Add file to existing vector store
        const vectorStoresAPI = openai.vectorStores || openai.beta.vectorStores;
        await vectorStoresAPI.files.create(vectorStoreId, {
          file_id: fileId
        });
      } else {
        // Create new vector store
        const vectorStoresAPI = openai.vectorStores || openai.beta.vectorStores;
        const vectorStore = await vectorStoresAPI.create({
          name: `knowledge-base-${assistantId}`,
          file_ids: [fileId]
        });
        
        vectorStoreId = vectorStore.id;
        
        // Update assistant with new vector store
        await openai.beta.assistants.update(assistantId, {
          tool_resources: {
            file_search: {
              vector_store_ids: [vectorStoreId]
            }
          }
        });
      }

      // Wait for vector store to be ready
      await this.waitForVectorStoreReady(vectorStoreId);

      return {
        success: true,
        fileId: fileId,
        vectorStoreId: vectorStoreId
      };
    } catch (error) {
      console.error('Update assistant knowledge base error:', error);
      throw new Error(`Failed to update assistant knowledge base: ${error.message}`);
    }
  }
}

module.exports = new AssistantService();