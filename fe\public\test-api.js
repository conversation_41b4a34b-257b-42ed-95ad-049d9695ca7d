// Test utility untuk browser console
// Buka browser console dan jalankan: testApi()

async function testApi() {
  const API_URL = 'http://localhost:3001';
  const TOKEN = 'abcde';
  
  console.log('🚀 Testing API Connection...');
  console.log('Backend URL:', API_URL);
  console.log('Token:', TOKEN);
  
  try {
    // Test 1: Health check
    console.log('\n1. Testing health check...');
    const healthResponse = await fetch(`${API_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    
    // Test 2: Chat with token
    console.log('\n2. Testing chat with token...');
    const chatResponse = await fetch(`${API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      },
      body: JSON.stringify({
        message: 'Test message from browser console',
        threadId: null
      })
    });
    
    if (chatResponse.ok) {
      const chatData = await chatResponse.json();
      console.log('✅ Chat response:', chatData);
    } else {
      const errorData = await chatResponse.json();
      console.log('❌ Chat error:', errorData);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    console.log('\n💡 Make sure:');
    console.log('- Backend is running: cd be && npm run dev');
    console.log('- Database is running and accessible');
    console.log('- No CORS issues');
  }
}

// Make it available globally
window.testApi = testApi;

console.log('🔧 Test utility loaded. Run testApi() to test backend connection.');