'use client';

import React, { useState } from 'react';
import { api } from '@/lib/api';

interface CreateExpertProps {
  onExpertCreated?: (expert: any) => void;
  onCancel?: () => void;
}

const CreateExpert: React.FC<CreateExpertProps> = ({ onExpertCreated, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    systemPrompt: '',
    model: 'gpt-4o-mini',
    pricingPercentage: '0.00',
    isPublic: false
  });
  const [labels, setLabels] = useState<string[]>([]);
  const [currentLabel, setCurrentLabel] = useState('');
  const [knowledgeBaseFile, setKnowledgeBaseFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const models = [
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
    { value: 'gpt-4o', label: 'GPT-4o' },
    { value: 'gpt-4o-mini', label: 'GPT-4o Mini' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddLabel = () => {
    if (currentLabel.trim() && labels.length < 5 && !labels.includes(currentLabel.trim())) {
      setLabels(prev => [...prev, currentLabel.trim()]);
      setCurrentLabel('');
    }
  };

  const handleRemoveLabel = (indexToRemove: number) => {
    setLabels(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleLabelKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddLabel();
    }
  };

  const handleKnowledgeBaseFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type for knowledge base
      const validTypes = ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/markdown', 'application/json'];
      const validExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];
      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
      
      if (validTypes.includes(selectedFile.type) || validExtensions.includes(fileExtension)) {
        setKnowledgeBaseFile(selectedFile);
        setError(null);
      } else {
        setError('Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON');
        setKnowledgeBaseFile(null);
      }
    }
  };

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type for images
      const validImageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
      const validImageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
      
      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      
      if (selectedFile.size > maxSize) {
        setError('Image file size must be less than 10MB');
        setImageFile(null);
        return;
      }
      
      if (validImageTypes.includes(selectedFile.type) || validImageExtensions.includes(fileExtension)) {
        setImageFile(selectedFile);
        setError(null);
      } else {
        setError('Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP');
        setImageFile(null);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('systemPrompt', formData.systemPrompt);
      formDataToSend.append('model', formData.model);
      formDataToSend.append('pricingPercentage', formData.pricingPercentage);
      formDataToSend.append('isPublic', formData.isPublic.toString());
      formDataToSend.append('labels', JSON.stringify(labels));
      
      if (knowledgeBaseFile) {
        formDataToSend.append('file', knowledgeBaseFile);
      }
      
      if (imageFile) {
        formDataToSend.append('image', imageFile);
      }

      const result = await api.createExpert(formDataToSend);
      
      if (result.success) {
        onExpertCreated?.(result.expert);
        // Reset form
        setFormData({
          name: '',
          description: '',
          systemPrompt: '',
          model: 'gpt-4o-mini',
          pricingPercentage: '0.00',
          isPublic: false
        });
        setLabels([]);
        setCurrentLabel('');
        setKnowledgeBaseFile(null);
        setImageFile(null);
      } else {
        setError(result.error || 'Failed to create expert');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create expert');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-center mb-6">Create AI Expert</h2>
      <p className="text-gray-600 text-center mb-8">Fill in the details to create a new AI expert profile.</p>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="e.g., Creative Writer"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Describe the expert's capabilities and purpose."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Image Upload */}
        <div>
          <label htmlFor="imageFile" className="block text-sm font-medium text-gray-700 mb-2">
            Upload Image (Optional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              id="imageFile"
              onChange={handleImageFileChange}
              accept=".png,.jpg,.jpeg,.gif,.webp"
              className="hidden"
            />
            <label htmlFor="imageFile" className="cursor-pointer">
              <div className="text-gray-400 mb-2">
                <svg className="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20v-2a2 2 0 012-2h8a2 2 0 012 2v2m0 0v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8m8-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v2m0 0h12m0 0v8a2 2 0 01-2 2h-8a2 2 0 01-2-2v-8z" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <p className="text-blue-600 hover:text-blue-500">Upload an image or drag and drop</p>
              <p className="text-sm text-gray-500">PNG, JPG, GIF, WEBP up to 10MB</p>
            </label>
            {imageFile && (
              <p className="mt-2 text-sm text-green-600">Selected: {imageFile.name}</p>
            )}
          </div>
        </div>

        {/* Knowledge Base Upload */}
        <div>
          <label htmlFor="knowledgeBaseFile" className="block text-sm font-medium text-gray-700 mb-2">
            Upload Knowledge Base (Optional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              id="knowledgeBaseFile"
              onChange={handleKnowledgeBaseFileChange}
              accept=".pdf,.txt,.docx,.doc,.md,.json"
              className="hidden"
            />
            <label htmlFor="knowledgeBaseFile" className="cursor-pointer">
              <div className="text-gray-400 mb-2">
                <svg className="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <p className="text-blue-600 hover:text-blue-500">Upload a knowledge base file or drag and drop</p>
              <p className="text-sm text-gray-500">PDF, TXT, DOCX, DOC, MD, JSON</p>
            </label>
            {knowledgeBaseFile && (
              <p className="mt-2 text-sm text-green-600">Selected: {knowledgeBaseFile.name}</p>
            )}
          </div>
        </div>

        {/* System Prompt */}
        <div>
          <label htmlFor="systemPrompt" className="block text-sm font-medium text-gray-700 mb-2">
            System Prompt
          </label>
          <textarea
            id="systemPrompt"
            name="systemPrompt"
            value={formData.systemPrompt}
            onChange={handleInputChange}
            placeholder="Enter the system prompt that defines the expert's behavior."
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Model Selection */}
        <div>
          <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-2">
            Model Selection
          </label>
          <select
            id="model"
            name="model"
            value={formData.model}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {models.map(model => (
              <option key={model.value} value={model.value}>
                {model.label}
              </option>
            ))}
          </select>
        </div>

        {/* Pricing */}
        <div>
          <label htmlFor="pricingPercentage" className="block text-sm font-medium text-gray-700 mb-2">
            Pricing (% of token usage)
          </label>
          <div className="relative">
            <input
              type="number"
              id="pricingPercentage"
              name="pricingPercentage"
              value={formData.pricingPercentage}
              onChange={handleInputChange}
              min="0"
              max="100"
              step="0.01"
              className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="absolute right-3 top-2 text-gray-500">%</span>
          </div>
        </div>

        {/* Visibility */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="isPublic"
              checked={formData.isPublic}
              onChange={handleInputChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
            <span className="text-sm font-medium text-gray-700">
              Make this expert public (others can discover and use it)
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1 ml-7">
            {formData.isPublic ? 'This expert will be visible to all users' : 'This expert will be private (unlisted)'}
          </p>
        </div>

        {/* Labels */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Labels (max 5)
          </label>
          <div className="space-y-3">
            <div className="flex space-x-2">
              <input
                type="text"
                value={currentLabel}
                onChange={(e) => setCurrentLabel(e.target.value)}
                onKeyPress={handleLabelKeyPress}
                placeholder="Add a label..."
                maxLength={50}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={labels.length >= 5}
              />
              <button
                type="button"
                onClick={handleAddLabel}
                disabled={!currentLabel.trim() || labels.length >= 5 || labels.includes(currentLabel.trim())}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
            </div>
            
            {labels.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {labels.map((label, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {label}
                    <button
                      type="button"
                      onClick={() => handleRemoveLabel(index)}
                      className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
            
            <p className="text-xs text-gray-500">
              {labels.length}/5 labels used. Labels help users discover your expert.
            </p>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Creating...' : 'Create Expert'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateExpert;