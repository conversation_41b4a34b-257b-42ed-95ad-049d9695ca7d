{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/expert/%5Bid%5D/page.tsx"], "sourcesContent": ["import ExpertProfile from '@/components/ExpertProfile';\r\n\r\ninterface PageProps {\r\n  params: {\r\n    id: string;\r\n  };\r\n}\r\n\r\nexport default function ExpertProfilePage({ params }: PageProps) {\r\n  return <ExpertProfile expertId={params.id} />;\r\n}\r\n\r\nexport async function generateMetadata({ params }: PageProps) {\r\n  return {\r\n    title: `Expert Profile - AI Trainer Hub`,\r\n    description: `Connect with AI Expert ${params.id} for specialized assistance`,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQe,SAAS,kBAAkB,EAAE,MAAM,EAAa;IAC7D,qBAAO,8OAAC;QAAc,UAAU,OAAO,EAAE;;;;;;AAC3C;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAa;IAC1D,OAAO;QACL,OAAO,CAAC,+BAA+B,CAAC;QACxC,aAAa,CAAC,uBAAuB,EAAE,OAAO,EAAE,CAAC,2BAA2B,CAAC;IAC/E;AACF", "debugId": null}}]}