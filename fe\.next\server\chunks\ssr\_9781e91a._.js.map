{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/lib/api.ts"], "sourcesContent": ["const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\r\n\r\ninterface ApiOptions {\r\n    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\r\n    body?: any;\r\n    headers?: Record<string, string>;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n    const { method = 'GET', body, headers = {} } = options;\r\n\r\n    const fullUrl = `${API_URL}${endpoint}`;\r\n\r\n    // Debug logging\r\n    console.log('🔍 API Call Debug:', {\r\n        endpoint,\r\n        fullUrl,\r\n        API_URL,\r\n        TOKEN: TOKEN.substring(0, 3) + '***',\r\n        method,\r\n        body,\r\n        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,\r\n        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN\r\n    });\r\n\r\n    const config: RequestInit = {\r\n        method,\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${TOKEN}`,\r\n            ...headers,\r\n        },\r\n    };\r\n\r\n    if (body && method !== 'GET') {\r\n        config.body = JSON.stringify(body);\r\n    }\r\n\r\n    try {\r\n        const response = await fetch(fullUrl, config);\r\n\r\n        console.log('📡 Response status:', response.status, response.statusText);\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            console.error('❌ API Error:', errorData);\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('✅ API Success:', data);\r\n        return data;\r\n    } catch (error) {\r\n        console.error('💥 API call failed:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n    // Health check (no token required)\r\n    health: () => apiCall('/health'),\r\n\r\n    // Chat endpoints (token required)\r\n    chat: (message: string, threadId?: string, expertId?: string, expertContext?: any) => apiCall('/api/chat', {\r\n        method: 'POST',\r\n        body: { message, threadId, expertId, expertContext }\r\n    }),\r\n\r\n    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n    // Chat session endpoints\r\n    getUserChatSessions: (limit?: number) => apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ''}`),\r\n\r\n    updateSessionTitle: (sessionId: string, title: string) => apiCall(`/api/chat/sessions/${sessionId}/title`, {\r\n        method: 'PUT',\r\n        body: { title }\r\n    }),\r\n\r\n    deleteSession: (sessionId: string) => apiCall(`/api/chat/sessions/${sessionId}`, {\r\n        method: 'DELETE'\r\n    }),\r\n\r\n    // Assistant endpoints (token required)\r\n    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),\r\n\r\n    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {\r\n        method: 'POST',\r\n        body: { threadId, message }\r\n    }),\r\n\r\n    runAssistant: (threadId: string) => apiCall('/assistant/run', {\r\n        method: 'POST',\r\n        body: { threadId }\r\n    }),\r\n\r\n    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n    // Expert endpoints (token required)\r\n    createExpert: (expertData: FormData) => {\r\n        return fetch(`${API_URL}/api/experts`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: expertData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n\r\n    listExperts: () => apiCall('/api/experts'),\r\n\r\n    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {\r\n        const formData = new FormData();\r\n        \r\n        // Add text fields\r\n        Object.keys(expertData).forEach(key => {\r\n            if (expertData[key] !== undefined && expertData[key] !== null) {\r\n                if (key === 'labels' && Array.isArray(expertData[key])) {\r\n                    formData.append(key, JSON.stringify(expertData[key]));\r\n                } else {\r\n                    formData.append(key, expertData[key].toString());\r\n                }\r\n            }\r\n        });\r\n\r\n        // Add files\r\n        if (knowledgeBaseFile) {\r\n            formData.append('file', knowledgeBaseFile);\r\n        }\r\n        if (imageFile) {\r\n            formData.append('image', imageFile);\r\n        }\r\n\r\n        return fetch(`${API_URL}/api/experts/${expertId}`, {\r\n            method: 'PUT',\r\n            headers: {\r\n                'Authorization': `Bearer ${TOKEN}`,\r\n            },\r\n            body: formData\r\n        }).then(async response => {\r\n            if (!response.ok) {\r\n                const errorData = await response.json().catch(() => ({}));\r\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n            }\r\n            return response.json();\r\n        });\r\n    },\r\n};"], "names": [], "mappings": ";;;;AAAA,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAQxC,eAAe,QAAQ,QAAgB,EAAE,UAAsB,CAAC,CAAC;IACpE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG;IAE/C,MAAM,UAAU,GAAG,UAAU,UAAU;IAEvC,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAC9B;QACA;QACA;QACA,OAAO,MAAM,SAAS,CAAC,GAAG,KAAK;QAC/B;QACA;QACA,iCAAiC;QACjC,+BAA+B;IACnC;IAEA,MAAM,SAAsB;QACxB;QACA,SAAS;YACL,gBAAgB;YAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAClC,GAAG,OAAO;QACd;IACJ;IAEA,IAAI,QAAQ,WAAW,OAAO;QAC1B,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC;IACjC;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;QAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACjF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACV;AACJ;AAGO,MAAM,MAAM;IACf,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CAAC,SAAiB,UAAmB,UAAmB,gBAAwB,QAAQ,aAAa;YACvG,QAAQ;YACR,MAAM;gBAAE;gBAAS;gBAAU;gBAAU;YAAc;QACvD;IAEA,mBAAmB,CAAC,WAAqB,QAAQ,CAAC,YAAY,EAAE,SAAS,SAAS,CAAC;IAEnF,yBAAyB;IACzB,qBAAqB,CAAC,QAAmB,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAEtG,oBAAoB,CAAC,WAAmB,QAAkB,QAAQ,CAAC,mBAAmB,EAAE,UAAU,MAAM,CAAC,EAAE;YACvG,QAAQ;YACR,MAAM;gBAAE;YAAM;QAClB;IAEA,eAAe,CAAC,YAAsB,QAAQ,CAAC,mBAAmB,EAAE,WAAW,EAAE;YAC7E,QAAQ;QACZ;IAEA,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAAoB,QAAQ,sBAAsB;YAC9E,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC9B;IAEA,cAAc,CAAC,WAAqB,QAAQ,kBAAkB;YAC1D,QAAQ;YACR,MAAM;gBAAE;YAAS;QACrB;IAEA,aAAa,CAAC,WAAqB,QAAQ,CAAC,oBAAoB,EAAE,UAAU;IAE5E,oCAAoC;IACpC,cAAc,CAAC;QACX,OAAO,MAAM,GAAG,QAAQ,YAAY,CAAC,EAAE;YACnC,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACtC;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACjF;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;IAEA,aAAa,IAAM,QAAQ;IAE3B,WAAW,CAAC,WAAqB,QAAQ,CAAC,aAAa,EAAE,UAAU;IAEnE,cAAc,CAAC,UAAkB,YAAiB,mBAAiC;QAC/E,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC3D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACpD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACvD,OAAO;oBACH,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBACjD;YACJ;QACJ;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACnB,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,IAAI,WAAW;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,OAAO,MAAM,GAAG,QAAQ,aAAa,EAAE,UAAU,EAAE;YAC/C,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACtC;YACA,MAAM;QACV,GAAG,IAAI,CAAC,OAAM;YACV,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACjF;YACA,OAAO,SAAS,IAAI;QACxB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/ChatHistory.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { api } from '@/lib/api';\r\nimport { MessageCircle, Clock, Trash2, Edit2, User, Bot } from 'lucide-react';\r\n\r\ninterface ChatSession {\r\n  id: number;\r\n  thread_id: string;\r\n  expert_id: number | null;\r\n  expert_name: string | null;\r\n  expert_model: string | null;\r\n  session_title: string;\r\n  message_count: number;\r\n  last_message_at: string | null;\r\n  user_messages: number;\r\n  assistant_messages: number;\r\n  total_tokens: number;\r\n  total_cost: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nconst ChatHistory: React.FC = () => {\r\n  const [sessions, setSessions] = useState<ChatSession[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [editingSession, setEditingSession] = useState<number | null>(null);\r\n  const [newTitle, setNewTitle] = useState('');\r\n\r\n  const loadSessions = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      const result = await api.getUserChatSessions(50);\r\n      \r\n      if (result.success) {\r\n        setSessions(result.sessions);\r\n      } else {\r\n        setError(result.error || 'Failed to load chat sessions');\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to load chat sessions');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadSessions();\r\n  }, []);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - date.getTime();\r\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n    const diffDays = Math.floor(diffHours / 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${diffHours}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${diffDays}d ago`;\r\n    } else {\r\n      return date.toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleUpdateTitle = async (sessionId: number, title: string) => {\r\n    try {\r\n      const result = await api.updateSessionTitle(sessionId.toString(), title);\r\n      if (result.success) {\r\n        setSessions(sessions.map(session => \r\n          session.id === sessionId \r\n            ? { ...session, session_title: title }\r\n            : session\r\n        ));\r\n        setEditingSession(null);\r\n        setNewTitle('');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update title:', error);\r\n    }\r\n  };\r\n\r\n  const handleDeleteSession = async (sessionId: number) => {\r\n    if (!confirm('Are you sure you want to delete this chat session?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const result = await api.deleteSession(sessionId.toString());\r\n      if (result.success) {\r\n        setSessions(sessions.filter(session => session.id !== sessionId));\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete session:', error);\r\n    }\r\n  };\r\n\r\n  const startEditing = (session: ChatSession) => {\r\n    setEditingSession(session.id);\r\n    setNewTitle(session.session_title);\r\n  };\r\n\r\n  const cancelEditing = () => {\r\n    setEditingSession(null);\r\n    setNewTitle('');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"mb-8\">\r\n            <Link \r\n              href=\"/\"\r\n              className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n            >\r\n              <span>←</span>\r\n              <span>Back to Home</span>\r\n            </Link>\r\n          </div>\r\n\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Chat History</h1>\r\n\r\n          <div className=\"space-y-4\">\r\n            {[...Array(5)].map((_, index) => (\r\n              <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 animate-pulse\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"h-5 bg-gray-200 rounded w-1/3\"></div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\r\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"mb-8\">\r\n            <Link \r\n              href=\"/\"\r\n              className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n            >\r\n              <span>←</span>\r\n              <span>Back to Home</span>\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto\">\r\n              <div className=\"text-red-600 text-6xl mb-4\">⚠️</div>\r\n              <h3 className=\"text-xl font-semibold text-red-800 mb-2\">Unable to Load Chat History</h3>\r\n              <p className=\"text-red-600 mb-4\">{error}</p>\r\n              <button \r\n                onClick={loadSessions}\r\n                className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n        <div className=\"mb-8\">\r\n          <Link \r\n            href=\"/\"\r\n            className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors\"\r\n          >\r\n            <span>←</span>\r\n            <span>Back to Home</span>\r\n          </Link>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900\">Chat History</h1>\r\n          <div className=\"text-sm text-gray-500\">\r\n            {sessions.length} conversation{sessions.length !== 1 ? 's' : ''}\r\n          </div>\r\n        </div>\r\n\r\n        {sessions.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"text-6xl mb-4\">💬</div>\r\n            <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">No Chat History Yet</h3>\r\n            <p className=\"text-gray-500 mb-6\">Start a conversation with an AI expert to see it here!</p>\r\n            <Link \r\n              href=\"/\"\r\n              className=\"inline-block px-6 py-3 rounded-xl text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5\"\r\n              style={{ backgroundColor: '#1E3A8A' }}\r\n            >\r\n              Browse Experts\r\n            </Link>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-4\">\r\n            {sessions.map((session) => (\r\n              <div\r\n                key={session.id}\r\n                className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200 group\"\r\n              >\r\n                <div className=\"flex items-start justify-between mb-4\">\r\n                  <div className=\"flex-1\">\r\n                    {editingSession === session.id ? (\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          value={newTitle}\r\n                          onChange={(e) => setNewTitle(e.target.value)}\r\n                          className=\"flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                          onKeyDown={(e) => {\r\n                            if (e.key === 'Enter') {\r\n                              handleUpdateTitle(session.id, newTitle);\r\n                            } else if (e.key === 'Escape') {\r\n                              cancelEditing();\r\n                            }\r\n                          }}\r\n                          autoFocus\r\n                        />\r\n                        <button\r\n                          onClick={() => handleUpdateTitle(session.id, newTitle)}\r\n                          className=\"px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\r\n                        >\r\n                          Save\r\n                        </button>\r\n                        <button\r\n                          onClick={cancelEditing}\r\n                          className=\"px-3 py-1 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors text-sm\"\r\n                        >\r\n                          Cancel\r\n                        </button>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"flex items-center space-x-3\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-900 transition-colors\">\r\n                          {session.session_title}\r\n                        </h3>\r\n                        <button\r\n                          onClick={() => startEditing(session)}\r\n                          className=\"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 transition-all\"\r\n                        >\r\n                          <Edit2 className=\"w-4 h-4\" />\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {session.expert_name && (\r\n                      <div className=\"flex items-center space-x-2 mt-2\">\r\n                        <div \r\n                          className=\"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs\"\r\n                          style={{ backgroundColor: '#1E3A8A' }}\r\n                        >\r\n                          <Bot className=\"w-3 h-3\" />\r\n                        </div>\r\n                        <span className=\"text-sm text-gray-600\">\r\n                          Expert: <span className=\"font-medium\">{session.expert_name}</span>\r\n                        </span>\r\n                        <span className=\"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full\">\r\n                          {session.expert_model}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center space-x-2 ml-4\">\r\n                    <Link\r\n                      href={`/chat?threadId=${session.thread_id}`}\r\n                      className=\"px-4 py-2 rounded-lg text-white font-medium transition-all duration-200 hover:shadow-md\"\r\n                      style={{ backgroundColor: '#1E3A8A' }}\r\n                    >\r\n                      Continue\r\n                    </Link>\r\n                    <button\r\n                      onClick={() => handleDeleteSession(session.id)}\r\n                      className=\"p-2 text-gray-400 hover:text-red-600 transition-colors\"\r\n                    >\r\n                      <Trash2 className=\"w-4 h-4\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <MessageCircle className=\"w-4 h-4\" />\r\n                      <span>{session.message_count} messages</span>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <User className=\"w-4 h-4\" />\r\n                      <span>{session.user_messages}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <Bot className=\"w-4 h-4\" />\r\n                      <span>{session.assistant_messages}</span>\r\n                    </div>\r\n                    {session.total_tokens > 0 && (\r\n                      <div>\r\n                        <span>🔢 {session.total_tokens} tokens</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Clock className=\"w-4 h-4\" />\r\n                    <span>\r\n                      {session.last_message_at \r\n                        ? formatDate(session.last_message_at)\r\n                        : formatDate(session.created_at)\r\n                      }\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatHistory;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAwBA,MAAM,cAAwB;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,SAAS,MAAM,iHAAA,CAAA,MAAG,CAAC,mBAAmB,CAAC;YAE7C,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,QAAQ;YAC7B,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,EAAE;QACrD,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,UAAU,KAAK,CAAC;QAC5B,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,SAAS,KAAK,CAAC;QAC3B,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,OAAO;gBACP,KAAK;gBACL,MAAM,KAAK,WAAW,OAAO,IAAI,WAAW,KAAK,YAAY;YAC/D;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO,WAAmB;QAClD,IAAI;YACF,MAAM,SAAS,MAAM,iHAAA,CAAA,MAAG,CAAC,kBAAkB,CAAC,UAAU,QAAQ,IAAI;YAClE,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,SAAS,GAAG,CAAC,CAAA,UACvB,QAAQ,EAAE,KAAK,YACX;wBAAE,GAAG,OAAO;wBAAE,eAAe;oBAAM,IACnC;gBAEN,kBAAkB;gBAClB,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,uDAAuD;YAClE;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,iHAAA,CAAA,MAAG,CAAC,aAAa,CAAC,UAAU,QAAQ;YACzD,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB,QAAQ,EAAE;QAC5B,YAAY,QAAQ,aAAa;IACnC;IAEA,MAAM,gBAAgB;QACpB,kBAAkB;QAClB,YAAY;IACd;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;;;;;;kCAIV,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAPT;;;;;;;;;;;;;;;;;;;;;IAetB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM;gCAAC;gCAAc,SAAS,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;gBAIhE,SAAS,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAAU;sCACrC;;;;;;;;;;;6EAKH,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,WAAW,CAAC;gEACV,IAAI,EAAE,GAAG,KAAK,SAAS;oEACrB,kBAAkB,QAAQ,EAAE,EAAE;gEAChC,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oEAC7B;gEACF;4DACF;4DACA,SAAS;;;;;;sEAEX,8OAAC;4DACC,SAAS,IAAM,kBAAkB,QAAQ,EAAE,EAAE;4DAC7C,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;6GAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,aAAa;;;;;;sEAExB,8OAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;sEAEV,cAAA,8OAAC,kMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAKtB,QAAQ,WAAW,kBAClB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAU;sEAEpC,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,8OAAC;4DAAK,WAAU;;gEAAwB;8EAC9B,8OAAC;oEAAK,WAAU;8EAAe,QAAQ,WAAW;;;;;;;;;;;;sEAE5D,8OAAC;4DAAK,WAAU;sEACb,QAAQ,YAAY;;;;;;;;;;;;;;;;;;sDAM7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,eAAe,EAAE,QAAQ,SAAS,EAAE;oDAC3C,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAU;8DACrC;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;oDAC7C,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,8OAAC;;gEAAM,QAAQ,aAAa;gEAAC;;;;;;;;;;;;;8DAE/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAM,QAAQ,aAAa;;;;;;;;;;;;8DAE9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;sEAAM,QAAQ,kBAAkB;;;;;;;;;;;;gDAElC,QAAQ,YAAY,GAAG,mBACtB,8OAAC;8DACC,cAAA,8OAAC;;4DAAK;4DAAI,QAAQ,YAAY;4DAAC;;;;;;;;;;;;;;;;;;sDAKrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DACE,QAAQ,eAAe,GACpB,WAAW,QAAQ,eAAe,IAClC,WAAW,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;2BA7GlC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAyH/B;uCAEe", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/Icon.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/message-circle.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n];\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('message-circle', __iconNode);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/clock.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/pen.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z',\n      key: '1a8usu',\n    },\n  ],\n];\n\n/**\n * @component @name Pen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuMTc0IDYuODEyYTEgMSAwIDAgMC0zLjk4Ni0zLjk4N0wzLjg0MiAxNi4xNzRhMiAyIDAgMCAwLS41LjgzbC0xLjMyMSA0LjM1MmEuNS41IDAgMCAwIC42MjMuNjIybDQuMzUzLTEuMzJhMiAyIDAgMCAwIC44My0uNDk3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pen = createLucideIcon('pen', __iconNode);\n\nexport default Pen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/user.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/bot.js", "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/node_modules/lucide-react/src/icons/bot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 8V4H8', key: 'hb8ula' }],\n  ['rect', { width: '16', height: '12', x: '4', y: '8', rx: '2', key: 'enze0r' }],\n  ['path', { d: 'M2 14h2', key: 'vft8re' }],\n  ['path', { d: 'M20 14h2', key: '4cs60a' }],\n  ['path', { d: 'M15 13v2', key: '1xurst' }],\n  ['path', { d: 'M9 13v2', key: 'rq6x2g' }],\n];\n\n/**\n * @component @name Bot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgOFY0SDgiIC8+CiAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiB4PSI0IiB5PSI4IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMiAxNGgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxNGgyIiAvPgogIDxwYXRoIGQ9Ik0xNSAxM3YyIiAvPgogIDxwYXRoIGQ9Ik05IDEzdjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bot = createLucideIcon('bot', __iconNode);\n\nexport default Bot;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}