# Chat API

Endpoint untuk mengirim pesan ke assistant dan mendapa<PERSON>kan response.

## Endpoint
```
POST /api/chat
```

## Request

### Headers
```
Content-Type: application/json
```

### Body Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| message | string | Yes | Pesan yang akan dikirim ke assistant |
| threadId | string | No | ID thread untuk melanjutkan percakapan. Jika tidak ada, thread baru akan dibuat |
| userId | string | Yes | ID user untuk tracking dan isolasi data |

### Request Body Example
```json
{
  "message": "Halo, bagaimana cara membuat aplikasi web?",
  "threadId": "thread_abc123",
  "userId": "user_123"
}
```

## Response

### Success (200)
```json
{
  "response": "Untuk membuat aplikasi web, Anda bisa menggunakan...",
  "threadId": "thread_abc123",
  "status": "success",
  "usage": {
    "prompt_tokens": 150,
    "completion_tokens": 200,
    "total_tokens": 350
  }
}
```

### Error (400) - Missing Message
```json
{
  "error": "Message is required"
}
```

### Error (400) - Missing User ID
```json
{
  "error": "User ID is required"
}
```

### Error (500) - Server Error
```json
{
  "error": "Internal server error",
  "message": "Detailed error message"
}
```

## Response Fields
| Field | Type | Description |
|-------|------|-------------|
| response | string | Response dari assistant (sudah dibersihkan dari citation patterns) |
| threadId | string | ID thread untuk percakapan |
| status | string | Status response ("success") |
| usage | object | Informasi penggunaan token |
| usage.prompt_tokens | number | Jumlah token untuk prompt |
| usage.completion_tokens | number | Jumlah token untuk completion |
| usage.total_tokens | number | Total token yang digunakan |

## Contoh Penggunaan

### cURL
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Jelaskan tentang React hooks",
    "threadId": "thread_abc123"
  }'
```

### JavaScript (Fetch)
```javascript
const response = await fetch('http://localhost:3001/api/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: 'Jelaskan tentang React hooks',
    threadId: 'thread_abc123' // optional
  })
});

const data = await response.json();
console.log(data);
```

## Catatan
- Response dari assistant sudah dibersihkan dari citation patterns seperti `【1:1†source†L1-L5】`
- Jika threadId tidak disediakan, sistem akan membuat thread baru
- Thread ID dapat digunakan untuk melanjutkan percakapan yang sama