'use client';

import React, { useState } from 'react';
import C<PERSON><PERSON>x<PERSON> from './CreateExpert';
import <PERSON>Ex<PERSON> from './EditExpert';
import ExpertList from './ExpertList';

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

const ExpertPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'list' | 'create' | 'edit'>('list');
  const [selectedExpert, setSelectedExpert] = useState<Expert | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleExpertCreated = (expert: any) => {
    console.log('Expert created:', expert);
    setRefreshTrigger(prev => prev + 1);
    setActiveTab('list');
  };

  const handleExpertUpdated = (expert: any) => {
    console.log('Expert updated:', expert);
    setRefreshTrigger(prev => prev + 1);
    setActiveTab('list');
    setSelectedExpert(null);
  };

  const handleExpertSelect = (expert: Expert) => {
    console.log('Expert selected:', expert);
    // You can add navigation or modal logic here
  };

  const handleExpertEdit = (expert: Expert) => {
    console.log('Expert edit:', expert);
    setSelectedExpert(expert);
    setActiveTab('edit');
  };

  const handleCancelEdit = () => {
    setSelectedExpert(null);
    setActiveTab('list');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('list')}
          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'list'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          My Experts
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'create'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          Create Expert
        </button>
        {activeTab === 'edit' && selectedExpert && (
          <button
            className="px-6 py-3 font-medium text-sm border-b-2 border-orange-600 text-orange-600"
          >
            Edit: {selectedExpert.name}
          </button>
        )}
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'list' && (
          <ExpertList 
            onExpertSelect={handleExpertSelect}
            onExpertEdit={handleExpertEdit}
            refreshTrigger={refreshTrigger}
          />
        )}
        
        {activeTab === 'create' && (
          <CreateExpert 
            onExpertCreated={handleExpertCreated}
            onCancel={() => setActiveTab('list')}
          />
        )}

        {activeTab === 'edit' && selectedExpert && (
          <EditExpert 
            expert={selectedExpert}
            onExpertUpdated={handleExpertUpdated}
            onCancel={handleCancelEdit}
          />
        )}
      </div>
    </div>
  );
};

export default ExpertPanel;