const { pool } = require('../config/database');

async function initializeDatabase() {
  try {
    // Create user table if not exists
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS user (
        user_id INT AUTO_INCREMENT PRIMARY KEY,
        token VARCHAR(255) UNIQUE NOT NULL,
        nama VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Insert sample user with token 'abcde' if not exists
    await pool.execute(`
      INSERT IGNORE INTO user (token, nama) VALUES ('abcde', 'Test User')
    `);

    console.log('Database initialized successfully');
    console.log('Sample user created with token: abcde');
  } catch (error) {
    console.error('Database initialization failed:', error.message);
  }
}

module.exports = { initializeDatabase };