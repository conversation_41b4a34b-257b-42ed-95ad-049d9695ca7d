const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';

interface ApiOptions {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: any;
    headers?: Record<string, string>;
}

export async function apiCall(endpoint: string, options: ApiOptions = {}) {
    const { method = 'GET', body, headers = {} } = options;

    const fullUrl = `${API_URL}${endpoint}`;

    // Debug logging
    console.log('🔍 API Call Debug:', {
        endpoint,
        fullUrl,
        API_URL,
        TOKEN: TOKEN.substring(0, 3) + '***',
        method,
        body,
        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,
        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN
    });

    const config: RequestInit = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TOKEN}`,
            ...headers,
        },
    };

    if (body && method !== 'GET') {
        config.body = JSON.stringify(body);
    }

    try {
        const response = await fetch(fullUrl, config);

        console.log('📡 Response status:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('❌ API Error:', errorData);
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ API Success:', data);
        return data;
    } catch (error) {
        console.error('💥 API call failed:', error);
        throw error;
    }
}

// Specific API functions
export const api = {
    // Health check (no token required)
    health: () => apiCall('/health'),

    // Chat endpoints (token required)
    chat: (message: string, threadId?: string, expertId?: string, expertContext?: any) => apiCall('/api/chat', {
        method: 'POST',
        body: { message, threadId, expertId, expertContext }
    }),

    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),

    // Chat session endpoints
    getUserChatSessions: (limit?: number) => apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ''}`),

    getActiveSessionForExpert: (expertId: string) => apiCall(`/api/chat/sessions/expert/${expertId}`),

    updateSessionTitle: (sessionId: string, title: string) => apiCall(`/api/chat/sessions/${sessionId}/title`, {
        method: 'PUT',
        body: { title }
    }),

    deleteSession: (sessionId: string) => apiCall(`/api/chat/sessions/${sessionId}`, {
        method: 'DELETE'
    }),

    // Assistant endpoints (token required)
    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),

    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {
        method: 'POST',
        body: { threadId, message }
    }),

    runAssistant: (threadId: string) => apiCall('/assistant/run', {
        method: 'POST',
        body: { threadId }
    }),

    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),

    // Expert endpoints (token required)
    createExpert: (expertData: FormData) => {
        return fetch(`${API_URL}/api/experts`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
            },
            body: expertData
        }).then(async response => {
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },

    listExperts: () => apiCall('/api/experts'),

    getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),

    updateExpert: (expertId: number, expertData: any, knowledgeBaseFile?: File | null, imageFile?: File | null) => {
        const formData = new FormData();
        
        // Add text fields
        Object.keys(expertData).forEach(key => {
            if (expertData[key] !== undefined && expertData[key] !== null) {
                if (key === 'labels' && Array.isArray(expertData[key])) {
                    formData.append(key, JSON.stringify(expertData[key]));
                } else {
                    formData.append(key, expertData[key].toString());
                }
            }
        });

        // Add files
        if (knowledgeBaseFile) {
            formData.append('file', knowledgeBaseFile);
        }
        if (imageFile) {
            formData.append('image', imageFile);
        }

        return fetch(`${API_URL}/api/experts/${expertId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
            },
            body: formData
        }).then(async response => {
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
};