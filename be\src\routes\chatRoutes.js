const express = require('express');
const chatController = require('../controllers/chatController');

const router = express.Router();

// Health check endpoint
router.get('/health', chatController.healthCheck);
router.get('/test-db', chatController.testDatabase);

// Chat endpoints
router.post('/api/chat', chatController.chat);
router.get('/api/thread/:threadId/messages', chatController.getThreadMessages);

// Chat session endpoints
router.get('/api/chat/sessions', chatController.getUserSessions);
router.get('/api/chat/sessions/expert/:expertId', chatController.getActiveSessionForExpert);
router.put('/api/chat/sessions/:sessionId/title', chatController.updateSessionTitle);
router.delete('/api/chat/sessions/:sessionId', chatController.deleteSession);

module.exports = router;