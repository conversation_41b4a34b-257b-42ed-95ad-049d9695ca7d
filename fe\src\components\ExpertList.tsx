'use client';

import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface ExpertListProps {
  onExpertSelect?: (expert: Expert) => void;
  onExpertEdit?: (expert: Expert) => void;
  refreshTrigger?: number;
}

const ExpertList: React.FC<ExpertListProps> = ({ onExpertSelect, onExpertEdit, refreshTrigger }) => {
  const [experts, setExperts] = useState<Expert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadExperts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await api.listExperts();
      
      if (result.success) {
        setExperts(result.experts);
      } else {
        setError(result.error || 'Failed to load experts');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load experts');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadExperts();
  }, [refreshTrigger]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading experts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        <p>Error: {error}</p>
        <button 
          onClick={loadExperts}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (experts.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        <p>No experts created yet.</p>
        <p className="text-sm mt-2">Create your first AI expert to get started!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold mb-4">Your AI Experts ({experts.length})</h3>
      
      <div className="grid gap-4">
        {experts.map((expert) => (
          <div
            key={expert.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-semibold text-lg">{expert.name}</h4>
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  {expert.model}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onExpertEdit?.(expert);
                  }}
                  className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded hover:bg-green-200 transition-colors"
                >
                  Edit
                </button>
              </div>
            </div>
            
            {expert.description && (
              <p className="text-gray-600 text-sm mb-2">{expert.description}</p>
            )}

            {/* Labels */}
            {expert.labels && expert.labels.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {expert.labels.map((label, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                  >
                    {label}
                  </span>
                ))}
              </div>
            )}
            
            <div className="text-xs text-gray-500 space-y-1">
              <p>Assistant ID: {expert.assistantId}</p>
              <p>Pricing: {expert.pricingPercentage}% of token usage</p>
              <p>Public: {expert.isPublic ? 'Yes' : 'No'}</p>
              <p>Created: {formatDate(expert.createdAt)}</p>
              {expert.updatedAt !== expert.createdAt && (
                <p>Updated: {formatDate(expert.updatedAt)}</p>
              )}
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-100">
              <p className="text-xs text-gray-600">
                <strong>System Prompt:</strong> {expert.systemPrompt.substring(0, 100)}
                {expert.systemPrompt.length > 100 && '...'}
              </p>
            </div>

            {/* Image */}
            {expert.imageUrl && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <img 
                  src={`http://localhost:3001${expert.imageUrl}`} 
                  alt={expert.name} 
                  className="w-16 h-16 object-cover rounded border"
                />
              </div>
            )}

            {/* Chat Button */}
            <div className="mt-3 pt-3 border-t border-gray-100">
              <button
                onClick={() => onExpertSelect?.(expert)}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Chat with Expert
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExpertList;