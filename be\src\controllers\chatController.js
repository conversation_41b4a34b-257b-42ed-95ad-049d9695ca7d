const chatService = require('../services/chatService');
const ChatSessionService = require('../services/chatSessionService');

class ChatController {
  async chat(req, res) {
    try {
      const { message, threadId, expertId, expertContext } = req.body;

      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      // Get userId from authenticated user (set by auth middleware)
      const userId = req.user.user_id;

      // Create or get chat session
      const sessionResult = await ChatSessionService.createOrGetSession(userId, expertId, threadId);
      
      if (!sessionResult.success) {
        return res.status(500).json({ error: sessionResult.error });
      }

      const session = sessionResult.session;
      let currentThreadId = session.thread_id;

      // Save user message to database first
      const saveUserMessageResult = await ChatSessionService.saveMessage(
        session.id, 
        currentThreadId, 
        'user', 
        message, 
        0, // tokens will be calculated by the AI service
        0  // cost will be calculated based on expert pricing
      );

      if (!saveUserMessageResult.success) {
        return res.status(500).json({ error: saveUserMessageResult.error });
      }

      // Prepare expert context
      const finalExpertContext = expertContext || sessionResult.expertContext;

      // Process chat with AI service
      const result = await chatService.processChat(
        message, 
        currentThreadId.startsWith('temp_') ? null : currentThreadId, // Don't use temp thread ID for OpenAI
        userId,
        finalExpertContext
      );

      if (result.success && result.response) {
        // If we got a new thread ID from OpenAI and we had a temp thread ID, update it
        if (result.isNewThread && currentThreadId.startsWith('temp_')) {
          await ChatSessionService.updateThreadId(session.id, result.threadId);
          currentThreadId = result.threadId;
        }

        // Save AI response to database
        await ChatSessionService.saveMessage(
          session.id,
          currentThreadId,
          'assistant',
          result.response,
          result.tokensUsed || 0,
          result.cost || 0
        );

        // Return response with thread ID
        res.json({
          success: true,
          response: result.response,
          threadId: currentThreadId,
          sessionId: session.id,
          isNewSession: sessionResult.isNew,
          tokensUsed: result.tokensUsed || 0,
          cost: result.cost || 0
        });
      } else {
        res.status(500).json({ 
          error: result.error || 'Failed to process chat'
        });
      }

    } catch (error) {
      console.error('Chat error:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        message: error.message 
      });
    }
  }

  async getThreadMessages(req, res) {
    try {
      const { threadId } = req.params;
      const userId = req.user.user_id;

      // Get messages from database
      const result = await ChatSessionService.getChatHistory(threadId, userId);
      
      if (result.success) {
        res.json({ 
          success: true,
          messages: result.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.created_at,
            tokensUsed: msg.tokens_used,
            cost: msg.cost
          }))
        });
      } else {
        res.status(500).json({ error: result.error });
      }
    } catch (error) {
      console.error('Get messages error:', error);
      res.status(500).json({ 
        error: 'Failed to get messages',
        message: error.message 
      });
    }
  }

  async getUserSessions(req, res) {
    try {
      const userId = req.user.user_id;
      const limit = Math.min(parseInt(req.query.limit) || 20, 100); // Max 100 sessions

      console.log('Getting user sessions for:', userId, 'limit:', limit);
      
      const result = await ChatSessionService.getUserSessions(userId, limit);
      
      if (result.success) {
        res.json({ 
          success: true,
          sessions: result.sessions
        });
      } else {
        console.error('getUserSessions error:', result.error);
        res.status(500).json({ error: result.error });
      }
    } catch (error) {
      console.error('Get user sessions error:', error);
      res.status(500).json({ 
        error: 'Failed to get user sessions',
        message: error.message 
      });
    }
  }

  async updateSessionTitle(req, res) {
    try {
      const { sessionId } = req.params;
      const { title } = req.body;
      const userId = req.user.user_id;

      if (!title) {
        return res.status(400).json({ error: 'Title is required' });
      }

      const result = await ChatSessionService.updateSessionTitle(sessionId, userId, title);
      
      if (result.success) {
        res.json({ success: true });
      } else {
        res.status(500).json({ error: result.error });
      }
    } catch (error) {
      console.error('Update session title error:', error);
      res.status(500).json({ 
        error: 'Failed to update session title',
        message: error.message 
      });
    }
  }

  async deleteSession(req, res) {
    try {
      const { sessionId } = req.params;
      const userId = req.user.user_id;

      const result = await ChatSessionService.deleteSession(sessionId, userId);
      
      if (result.success) {
        res.json({ success: true });
      } else {
        res.status(500).json({ error: result.error });
      }
    } catch (error) {
      console.error('Delete session error:', error);
      res.status(500).json({ 
        error: 'Failed to delete session',
        message: error.message 
      });
    }
  }

  async healthCheck(req, res) {
    res.json({ status: 'OK', message: 'Backend server is running' });
  }

  async getActiveSessionForExpert(req, res) {
    try {
      const { expertId } = req.params;
      const userId = req.user.user_id;

      const result = await ChatSessionService.getActiveSessionForExpert(userId, expertId);
      
      if (!result.success) {
        return res.status(500).json({ error: result.error });
      }

      res.json(result);
    } catch (error) {
      console.error('Error getting active session for expert:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async testDatabase(req, res) {
    try {
      const db = require('../config/database');
      
      // Test basic database connection
      const [result] = await db.execute('SELECT 1 as test');
      
      // Test if chat_sessions table exists
      const [tables] = await db.execute(
        "SHOW TABLES LIKE 'chat_sessions'"
      );
      
      const tablesExist = tables.length > 0;
      
      // Test getting sessions count
      let sessionCount = 0;
      if (tablesExist) {
        const [count] = await db.execute(
          'SELECT COUNT(*) as count FROM chat_sessions'
        );
        sessionCount = count[0].count;
      }
      
      res.json({
        success: true,
        database: 'connected',
        tables: {
          chat_sessions: tablesExist,
          session_count: sessionCount
        },
        test_query: result[0]
      });
      
    } catch (error) {
      console.error('Database test error:', error);
      res.status(500).json({
        success: false,
        error: error.message,
        stack: error.stack
      });
    }
  }
}

module.exports = new ChatController();