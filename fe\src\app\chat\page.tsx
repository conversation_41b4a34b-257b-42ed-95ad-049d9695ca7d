"use client";

import { useState, useRef, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { useMutation } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Send, Loader2, ArrowLeft } from "lucide-react";
import { api } from "@/lib/api";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface Message {
  role: "user" | "assistant";
  content: string;
}

export default function ChatPage() {
  const searchParams = useSearchParams();
  const expertId = searchParams.get("expertId");
  const threadId = searchParams.get("threadId");
  
  const [expert, setExpert] = useState<Expert | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<number | null>(null);
  const [isLoadingExpert, setIsLoadingExpert] = useState(!!expertId);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const chatRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (expertId) {
      initializeChatWithExpert();
    } else if (threadId) {
      // Direct thread access (legacy support)
      loadChatHistory();
    }
  }, [expertId, threadId]);

  const initializeChatWithExpert = async () => {
    try {
      setIsLoadingExpert(true);
      setIsLoadingHistory(true);
      
      // First load expert information
      const expertResult = await api.getExpert(expertId!);
      if (!expertResult.success) {
        console.error('Failed to load expert:', expertResult);
        return;
      }
      
      setExpert(expertResult.expert);
      
      // Check for existing active session for this expert
      const sessionResult = await api.getActiveSessionForExpert(expertId!);
      
      if (sessionResult.success && sessionResult.session) {
        // Found existing session - load its history using session ID
        const existingSession = sessionResult.session;
        setCurrentThreadId(existingSession.thread_id);
        setCurrentSessionId(existingSession.id);
        
        // Load chat history for this session using the more reliable method
        await loadChatHistoryBySessionId(existingSession.id);
        
        console.log('Loaded existing session:', existingSession.id, 'with thread:', existingSession.thread_id);
      } else {
        // No existing session - show welcome message for new chat
        setMessages([{
          role: "assistant",
          content: `Hello! I'm ${expertResult.expert.name}. ${expertResult.expert.description} How can I assist you today?`
        }]);
        console.log('No existing session found, starting fresh chat');
      }
    } catch (error) {
      console.error('Failed to initialize chat with expert:', error);
    } finally {
      setIsLoadingExpert(false);
      setIsLoadingHistory(false);
    }
  };

  const loadChatHistory = async () => {
    if (!threadId) return;
    await loadChatHistoryForThread(threadId);
  };

  const loadChatHistoryForThread = async (targetThreadId: string) => {
    try {
      setIsLoadingHistory(true);
      const result = await api.getThreadMessages(targetThreadId);
      if (result.success && result.messages) {
        const formattedMessages = result.messages.map((msg: any) => ({
          role: msg.role,
          content: msg.content
        }));
        setMessages(formattedMessages);
        
        // If we don't have expert info but the thread has expert context, try to load it
        if (!expert && result.messages.length > 0) {
          // You might want to get expert info from the first message or session data
          // For now, we'll just show the messages
        }
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const loadChatHistoryBySessionId = async (sessionId: number) => {
    try {
      setIsLoadingHistory(true);
      // For now, we'll use the existing API which expects threadId
      // Later we can add a new API endpoint for sessionId-based loading
      const result = await api.getUserChatSessions(1); // Get the specific session
      // This is a temporary workaround - we should add a proper sessionId-based API
      // For now, let's just load by threadId
      if (currentThreadId) {
        await loadChatHistoryForThread(currentThreadId);
      }
    } catch (error) {
      console.error('Failed to load chat history by session:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const getExpertIcon = (labels: string[]) => {
    if (!labels) return '🤖';
    if (labels.includes('business') || labels.includes('marketing')) return '💼';
    if (labels.includes('code') || labels.includes('programming')) return '💻';
    if (labels.includes('creative') || labels.includes('design')) return '🎨';
    if (labels.includes('education') || labels.includes('learning')) return '📚';
    if (labels.includes('health') || labels.includes('medical')) return '🏥';
    if (labels.includes('finance') || labels.includes('money')) return '💰';
    return '🤖';
  };

  const sendMessage = useMutation({
    mutationFn: async ({ message, threadId: tId }: { message: string; threadId: string | null }) => {
      setIsLoading(true);

      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          message, 
          threadId: tId,
          expertId: expertId,
          expertContext: expert ? {
            name: expert.name,
            systemPrompt: expert.systemPrompt,
            model: expert.model
          } : undefined
        }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();

      // Update thread ID if we got a new one
      if (data.threadId) {
        setCurrentThreadId(data.threadId);
      }

      // Add the AI response to messages
      if (data.response) {
        setMessages((msgs) => [...msgs, { role: "assistant", content: data.response }]);
      }

      setIsLoading(false);
      return data;
    },
    onError: () => {
      setIsLoading(false);
    }
  });

  const handleSend = () => {
    if (!input.trim() || isLoading) return;
    setMessages((msgs) => [...msgs, { role: "user", content: input }]);
    sendMessage.mutate({ message: input, threadId: currentThreadId });
    setInput("");
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
    }, 100);
  };

  if (isLoadingExpert || isLoadingHistory) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#1E3A8A' }}></div>
          <p className="text-gray-600">Loading expert...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Link 
              href={expert ? `/expert/${expert.id}` : "/"}
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>{expert ? 'Back to Profile' : 'Back to Home'}</span>
            </Link>
            
            {expert && (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Chatting with</p>
                  <p className="font-semibold text-gray-900">{expert.name}</p>
                </div>
                {expert.imageUrl ? (
                  <img 
                    src={`http://localhost:3001${expert.imageUrl}`} 
                    alt={expert.name}
                    className="w-10 h-10 object-cover rounded-full border-2 border-gray-200"
                  />
                ) : (
                  <div 
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm"
                    style={{ backgroundColor: '#1E3A8A' }}
                  >
                    {getExpertIcon(expert.labels)}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Chat Interface */}
        <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
          <div className="p-6">
            {/* Expert Info Banner */}
            {expert && (
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                <div className="flex items-center space-x-4">
                  {expert.imageUrl ? (
                    <img 
                      src={`http://localhost:3001${expert.imageUrl}`} 
                      alt={expert.name}
                      className="w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm"
                    />
                  ) : (
                    <div 
                      className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm"
                      style={{ backgroundColor: '#1E3A8A' }}
                    >
                      {getExpertIcon(expert.labels)}
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{expert.name}</h3>
                    <p className="text-sm text-gray-600">{expert.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                        {expert.model}
                      </span>
                      <span className="text-xs text-green-600">● Online</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Chat Messages */}
            <div 
              ref={chatRef} 
              className="h-[60vh] overflow-y-auto space-y-4 mb-6 px-2"
              style={{ scrollbarWidth: 'thin' }}
            >
              {messages.length === 0 && !expert && (
                <div className="text-center mt-20">
                  <div className="text-6xl mb-4">💬</div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">Start a Conversation</h3>
                  <p className="text-gray-500">Choose an expert from the marketplace or start chatting!</p>
                </div>
              )}
              
              {messages.map((msg, i) => (
                <div key={i} className={`flex gap-3 ${msg.role === "user" ? "justify-end" : "justify-start"}`}>
                  {msg.role === "assistant" && (
                    <div className="flex-shrink-0">
                      {expert?.imageUrl ? (
                        <img 
                          src={`http://localhost:3001${expert.imageUrl}`} 
                          alt={expert.name}
                          className="w-8 h-8 object-cover rounded-full border border-gray-200"
                        />
                      ) : (
                        <div 
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                          style={{ backgroundColor: '#1E3A8A' }}
                        >
                          {expert ? getExpertIcon(expert.labels) : '🤖'}
                        </div>
                      )}
                    </div>
                  )}
                  
                  <div className={`rounded-2xl px-4 py-3 max-w-[70%] ${
                    msg.role === "user" 
                      ? "text-white shadow-lg" 
                      : "bg-gray-50 text-gray-900 border border-gray-100"
                  }`}
                  style={msg.role === "user" ? { backgroundColor: '#1E3A8A' } : {}}
                  >
                    <div 
                      className="text-sm leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: msg.content.replace(/\n/g, '<br/>') }} 
                    />
                  </div>
                  
                  {msg.role === "user" && (
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-sm">👤</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
              
              {isLoading && (
                <div className="flex gap-3 justify-start">
                  <div className="flex-shrink-0">
                    {expert?.imageUrl ? (
                      <img 
                        src={`http://localhost:3001${expert.imageUrl}`} 
                        alt={expert.name}
                        className="w-8 h-8 object-cover rounded-full border border-gray-200"
                      />
                    ) : (
                      <div 
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                        style={{ backgroundColor: '#1E3A8A' }}
                      >
                        {expert ? getExpertIcon(expert.labels) : '🤖'}
                      </div>
                    )}
                  </div>
                  <div className="rounded-2xl px-4 py-3 bg-gray-50 border border-gray-100 flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm text-gray-600">AI is thinking...</span>
                  </div>
                </div>
              )}
            </div>

            {/* Input Area */}
            <form 
              className="flex gap-3 items-end" 
              onSubmit={e => { e.preventDefault(); handleSend(); }}
            >
              <div className="flex-1">
                <Input
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  placeholder={expert ? `Ask ${expert.name} anything...` : "Type your message..."}
                  disabled={isLoading}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none"
                  autoFocus
                  onKeyDown={e => { 
                    if (e.key === "Enter" && !e.shiftKey) { 
                      e.preventDefault(); 
                      handleSend(); 
                    } 
                  }}
                />
              </div>
              <Button 
                type="submit" 
                disabled={isLoading || !input.trim()} 
                className="px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg"
                style={{ backgroundColor: '#1E3A8A' }}
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </Button>
            </form>

            {/* Usage Info */}
            {expert && (
              <div className="mt-4 text-center">
                <p className="text-xs text-gray-500">
                  💡 You're chatting with {expert.name} • Usage fee: {expert.pricingPercentage}% of tokens
                  {currentThreadId && (
                    <span className="ml-2">• Thread: {currentThreadId.substring(0, 8)}...</span>
                  )}
                </p>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
} 