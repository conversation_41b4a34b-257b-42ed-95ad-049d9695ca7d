const express = require('express');
const path = require('path');
require('dotenv').config();

const corsMiddleware = require('./middleware/cors');
const errorHandler = require('./middleware/errorHandler');
const { authenticateToken } = require('./middleware/auth');
const { testConnection } = require('./config/database');
const { initializeDatabase } = require('./utils/initDatabase');
const chatRoutes = require('./routes/chatRoutes');
const assistantRoutes = require('./routes/assistantRoutes');
const expertRoutes = require('./routes/expertRoutes');

const app = express();

// Test database connection and initialize on startup
testConnection();
initializeDatabase();

// Middleware
app.use(corsMiddleware);
app.use(express.json());

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check route (no auth required)
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

// Apply authentication middleware to all routes except health check
app.use(authenticateToken);

// Routes
app.use('/', chatRoutes);
app.use('/', assistantRoutes);
app.use('/', expertRoutes);

// Error handling middleware
app.use(errorHandler);

module.exports = app;