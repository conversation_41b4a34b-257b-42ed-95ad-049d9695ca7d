{"info": {"name": "AI Trainer Hub API", "description": "Collection untuk testing AI Trainer Hub Backend API", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "userId", "value": "user_123", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Chat - Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"<PERSON><PERSON>, bagaimana cara membuat aplikasi web?\",\n  \"threadId\": \"thread_abc123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chat", "host": ["{{baseUrl}}"], "path": ["api", "chat"]}}}, {"name": "Chat - Get Thread Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/thread/:threadId/messages", "host": ["{{baseUrl}}"], "path": ["api", "thread", ":threadId", "messages"], "variable": [{"key": "threadId", "value": "thread_abc123"}]}}}, {"name": "Assistant - Create (without file)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Customer Support Bot", "type": "text"}, {"key": "instructions", "value": "You are a helpful customer support assistant", "type": "text"}, {"key": "model", "value": "gpt-4o-mini", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/assistants", "host": ["{{baseUrl}}"], "path": ["api", "assistants"]}}}, {"name": "Assistant - Create (with file)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Document Assistant", "type": "text"}, {"key": "instructions", "value": "You are an assistant that helps with document analysis", "type": "text"}, {"key": "model", "value": "gpt-4o-mini", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/assistants", "host": ["{{baseUrl}}"], "path": ["api", "assistants"]}}}, {"name": "Assistant - List All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assistants", "host": ["{{baseUrl}}"], "path": ["api", "assistants"]}}}, {"name": "Assistant - Get by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assistants/:assistantId", "host": ["{{baseUrl}}"], "path": ["api", "assistants", ":assistantId"], "variable": [{"key": "assistantId", "value": "asst_abc123"}]}}}]}